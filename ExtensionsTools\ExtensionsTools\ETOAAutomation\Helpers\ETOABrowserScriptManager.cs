using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CefSharp;
using CefSharp.WinForms;
using ET;


namespace ET.ETOAAutomation.Helpers
{
    /// <summary>
    /// 浏览器脚本管理器，用于管理和执行复杂的自动化脚本
    /// </summary>
    public class ETOABrowserScriptManager
    {
        #region 私有字段
        private readonly ChromiumWebBrowser _browser;
        private readonly Dictionary<string, string> _scriptTemplates;
        private readonly Dictionary<string, object> _scriptVariables;
        #endregion

        #region 构造函数
        /// <summary>
        /// 初始化浏览器脚本管理器
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        public ETOABrowserScriptManager(ChromiumWebBrowser browser)
        {
            _browser = browser ?? throw new ArgumentNullException(nameof(browser));
            _scriptTemplates = new Dictionary<string, string>();
            _scriptVariables = new Dictionary<string, object>();
            
            InitializeDefaultScripts();
            ETLogManager.Info("ETOABrowserScriptManager", "浏览器脚本管理器初始化完成");
        }
        #endregion

        #region 脚本模板管理
        /// <summary>
        /// 初始化默认脚本模板
        /// </summary>
        private void InitializeDefaultScripts()
        {
            // 登录表单检测脚本
            _scriptTemplates["DetectLoginForm"] = @"
                (function() {
                    var forms = document.querySelectorAll('form');
                    var loginForms = [];
                    
                    forms.forEach(function(form) {
                        var inputs = form.querySelectorAll('input');
                        var hasPassword = false;
                        var hasUsername = false;
                        
                        inputs.forEach(function(input) {
                            if (input.type === 'password') hasPassword = true;
                            if (input.type === 'text' || input.type === 'email' || 
                                input.name.toLowerCase().includes('user') || 
                                input.name.toLowerCase().includes('email')) {
                                hasUsername = true;
                            }
                        });
                        
                        if (hasPassword && hasUsername) {
                            loginForms.push({
                                form: form,
                                action: form.action || window.location.href,
                                method: form.method || 'POST'
                            });
                        }
                    });
                    
                    return loginForms.length > 0 ? loginForms[0] : null;
                })();
            ";

            // 获取页面所有链接脚本
            _scriptTemplates["GetAllLinks"] = @"
                (function() {
                    var links = [];
                    var anchors = document.querySelectorAll('a[href]');
                    
                    anchors.forEach(function(anchor) {
                        links.push({
                            text: anchor.textContent.trim(),
                            href: anchor.href,
                            target: anchor.target || '_self'
                        });
                    });
                    
                    return links;
                })();
            ";

            // 获取页面所有表单脚本
            _scriptTemplates["GetAllForms"] = @"
                (function() {
                    var forms = [];
                    var formElements = document.querySelectorAll('form');
                    
                    formElements.forEach(function(form, index) {
                        var inputs = [];
                        var formInputs = form.querySelectorAll('input, select, textarea');
                        
                        formInputs.forEach(function(input) {
                            inputs.push({
                                name: input.name || input.id || 'input_' + inputs.length,
                                type: input.type || input.tagName.toLowerCase(),
                                value: input.value || '',
                                required: input.required || false
                            });
                        });
                        
                        forms.push({
                            index: index,
                            action: form.action || window.location.href,
                            method: form.method || 'POST',
                            inputs: inputs
                        });
                    });
                    
                    return forms;
                })();
            ";

            // 页面加载状态检测脚本
            _scriptTemplates["CheckPageLoadState"] = @"
                (function() {
                    return {
                        readyState: document.readyState,
                        isLoading: document.readyState !== 'complete',
                        hasImages: document.images.length > 0,
                        imagesLoaded: Array.from(document.images).every(img => img.complete),
                        title: document.title,
                        url: window.location.href
                    };
                })();
            ";

            // 获取页面元数据脚本
            _scriptTemplates["GetPageMetadata"] = @"
                (function() {
                    var metadata = {
                        title: document.title,
                        url: window.location.href,
                        description: '',
                        keywords: '',
                        author: '',
                        viewport: ''
                    };
                    
                    var metas = document.querySelectorAll('meta');
                    metas.forEach(function(meta) {
                        var name = meta.name || meta.property || '';
                        var content = meta.content || '';
                        
                        if (name.toLowerCase() === 'description') metadata.description = content;
                        if (name.toLowerCase() === 'keywords') metadata.keywords = content;
                        if (name.toLowerCase() === 'author') metadata.author = content;
                        if (name.toLowerCase() === 'viewport') metadata.viewport = content;
                    });
                    
                    return metadata;
                })();
            ";
        }

        /// <summary>
        /// 添加自定义脚本模板
        /// </summary>
        /// <param name="name">脚本名称</param>
        /// <param name="script">脚本内容</param>
        public void AddScriptTemplate(string name, string script)
        {
            try
            {
                _scriptTemplates[name] = script;
                ETLogManager.Info("ETOABrowserScriptManager", $"添加脚本模板: {name}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserScriptManager", $"添加脚本模板失败: {ex.Message}");
                throw new ETException("添加脚本模板失败", ex);
            }
        }

        /// <summary>
        /// 获取脚本模板
        /// </summary>
        /// <param name="name">脚本名称</param>
        /// <returns>脚本内容</returns>
        public string GetScriptTemplate(string name)
        {
            return _scriptTemplates.ContainsKey(name) ? _scriptTemplates[name] : null;
        }

        /// <summary>
        /// 获取所有脚本模板名称
        /// </summary>
        /// <returns>脚本名称列表</returns>
        public List<string> GetScriptTemplateNames()
        {
            return _scriptTemplates.Keys.ToList();
        }
        #endregion

        #region 脚本执行方法
        /// <summary>
        /// 执行脚本模板
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <param name="variables">变量字典</param>
        /// <returns>执行结果</returns>
        public async Task<object> ExecuteScriptTemplateAsync(string templateName, Dictionary<string, object> variables = null)
        {
            try
            {
                if (!_scriptTemplates.ContainsKey(templateName))
                {
                    throw new ArgumentException($"脚本模板不存在: {templateName}");
                }

                var script = _scriptTemplates[templateName];
                
                // 替换变量
                if (variables != null)
                {
                    foreach (var variable in variables)
                    {
                        var placeholder = $"{{{variable.Key}}}";
                        var value = variable.Value?.ToString() ?? "";
                        script = script.Replace(placeholder, value);
                    }
                }

                var result = await _browser.EvaluateScriptAsync(script);
                
                if (result.Success)
                {
                    ETLogManager.Info("ETOABrowserScriptManager", $"脚本执行成功: {templateName}");
                    return result.Result;
                }
                else
                {
                    ETLogManager.Error("ETOABrowserScriptManager", $"脚本执行失败: {result.Message}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserScriptManager", $"执行脚本模板失败: {ex.Message}");
                throw new ETException("执行脚本模板失败", ex);
            }
        }

        /// <summary>
        /// 执行自定义脚本
        /// </summary>
        /// <param name="script">脚本内容</param>
        /// <returns>执行结果</returns>
        public async Task<object> ExecuteCustomScriptAsync(string script)
        {
            try
            {
                var result = await _browser.EvaluateScriptAsync(script);
                
                if (result.Success)
                {
                    ETLogManager.Info("ETOABrowserScriptManager", "自定义脚本执行成功");
                    return result.Result;
                }
                else
                {
                    ETLogManager.Error("ETOABrowserScriptManager", $"自定义脚本执行失败: {result.Message}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserScriptManager", $"执行自定义脚本失败: {ex.Message}");
                throw new ETException("执行自定义脚本失败", ex);
            }
        }

        /// <summary>
        /// 批量执行脚本
        /// </summary>
        /// <param name="scripts">脚本列表</param>
        /// <returns>执行结果列表</returns>
        public async Task<List<object>> ExecuteScriptBatchAsync(List<string> scripts)
        {
            var results = new List<object>();
            
            try
            {
                foreach (var script in scripts)
                {
                    var result = await ExecuteCustomScriptAsync(script);
                    results.Add(result);
                    
                    // 脚本间延迟
                    await Task.Delay(100);
                }
                
                ETLogManager.Info("ETOABrowserScriptManager", $"批量脚本执行完成: {scripts.Count}个");
                return results;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserScriptManager", $"批量执行脚本失败: {ex.Message}");
                throw new ETException("批量执行脚本失败", ex);
            }
        }
        #endregion

        #region 高级功能方法
        /// <summary>
        /// 检测登录表单
        /// </summary>
        /// <returns>登录表单信息</returns>
        public async Task<LoginFormInfo> DetectLoginFormAsync()
        {
            try
            {
                var result = await ExecuteScriptTemplateAsync("DetectLoginForm");
                if (result != null)
                {
                    // 解析结果并返回登录表单信息
                    return ParseLoginFormInfo(result);
                }
                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserScriptManager", $"检测登录表单失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取页面所有链接
        /// </summary>
        /// <returns>链接信息列表</returns>
        public async Task<List<LinkInfo>> GetAllLinksAsync()
        {
            try
            {
                var result = await ExecuteScriptTemplateAsync("GetAllLinks");
                if (result != null)
                {
                    return ParseLinkInfoList(result);
                }
                return new List<LinkInfo>();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserScriptManager", $"获取页面链接失败: {ex.Message}");
                return new List<LinkInfo>();
            }
        }

        /// <summary>
        /// 获取页面元数据
        /// </summary>
        /// <returns>页面元数据</returns>
        public async Task<PageMetadata> GetPageMetadataAsync()
        {
            try
            {
                var result = await ExecuteScriptTemplateAsync("GetPageMetadata");
                if (result != null)
                {
                    return ParsePageMetadata(result);
                }
                return new PageMetadata();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserScriptManager", $"获取页面元数据失败: {ex.Message}");
                return new PageMetadata();
            }
        }

        /// <summary>
        /// 从文件加载脚本
        /// </summary>
        /// <param name="filePath">脚本文件路径</param>
        /// <returns>脚本内容</returns>
        public async Task<string> LoadScriptFromFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"脚本文件不存在: {filePath}");
                }

                var script = File.ReadAllText(filePath, Encoding.UTF8);
                ETLogManager.Info("ETOABrowserScriptManager", $"从文件加载脚本: {filePath}");
                return script;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserScriptManager", $"从文件加载脚本失败: {ex.Message}");
                throw new ETException("从文件加载脚本失败", ex);
            }
        }

        /// <summary>
        /// 保存脚本到文件
        /// </summary>
        /// <param name="script">脚本内容</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>保存是否成功</returns>
        public async Task<bool> SaveScriptToFileAsync(string script, string filePath)
        {
            try
            {
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(filePath, script, Encoding.UTF8);
                ETLogManager.Info("ETOABrowserScriptManager", $"脚本保存到文件: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserScriptManager", $"保存脚本到文件失败: {ex.Message}");
                return false;
            }
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 解析登录表单信息
        /// </summary>
        /// <param name="result">脚本执行结果</param>
        /// <returns>登录表单信息</returns>
        private LoginFormInfo ParseLoginFormInfo(object result)
        {
            try
            {
                // 这里需要根据实际的JavaScript返回结果进行解析
                // 简化实现，实际使用时可能需要更复杂的解析逻辑
                return new LoginFormInfo
                {
                    Found = result != null,
                    Action = "",
                    Method = "POST"
                };
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserScriptManager", $"解析登录表单信息失败: {ex.Message}");
                return new LoginFormInfo { Found = false };
            }
        }

        /// <summary>
        /// 解析链接信息列表
        /// </summary>
        /// <param name="result">脚本执行结果</param>
        /// <returns>链接信息列表</returns>
        private List<LinkInfo> ParseLinkInfoList(object result)
        {
            try
            {
                // 简化实现，实际使用时需要更完善的解析逻辑
                return new List<LinkInfo>();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserScriptManager", $"解析链接信息失败: {ex.Message}");
                return new List<LinkInfo>();
            }
        }

        /// <summary>
        /// 解析页面元数据
        /// </summary>
        /// <param name="result">脚本执行结果</param>
        /// <returns>页面元数据</returns>
        private PageMetadata ParsePageMetadata(object result)
        {
            try
            {
                // 简化实现，实际使用时需要更完善的解析逻辑
                return new PageMetadata
                {
                    Title = "",
                    Url = "",
                    Description = "",
                    Keywords = "",
                    Author = ""
                };
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserScriptManager", $"解析页面元数据失败: {ex.Message}");
                return new PageMetadata();
            }
        }
        #endregion

        #region 内部类
        /// <summary>
        /// 登录表单信息
        /// </summary>
        public class LoginFormInfo
        {
            public bool Found { get; set; }
            public string Action { get; set; }
            public string Method { get; set; }
        }

        /// <summary>
        /// 链接信息
        /// </summary>
        public class LinkInfo
        {
            public string Text { get; set; }
            public string Href { get; set; }
            public string Target { get; set; }
        }

        /// <summary>
        /// 页面元数据
        /// </summary>
        public class PageMetadata
        {
            public string Title { get; set; }
            public string Url { get; set; }
            public string Description { get; set; }
            public string Keywords { get; set; }
            public string Author { get; set; }
        }
        #endregion
    }
}
