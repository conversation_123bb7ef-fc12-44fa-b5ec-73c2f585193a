# 🚀 ETOAAutomation 集成使用指南

## 📋 概述

本指南详细介绍如何在项目中集成和使用ETOAAutomation模块，包括环境配置、初始化设置、基本使用方法、高级功能配置和常见问题解决方案。

## 🛠️ 环境要求

### 系统要求
- **操作系统**: Windows 10/11 (x64)
- **.NET Framework**: 4.7.2 或更高版本
- **内存**: 最少 4GB RAM，推荐 8GB 或更多
- **磁盘空间**: 至少 500MB 可用空间

### 依赖组件
- **CefSharp.WinForms**: v120.1.110 或更高版本
- **Flurl.Http**: v3.2.4 或更高版本
- **Newtonsoft.Json**: v13.0.3 或更高版本
- **ExtensionsTools**: 内部依赖库

## 📦 安装配置

### 1. 添加项目引用

```xml
<!-- 在项目文件中添加以下引用 -->
<ProjectReference Include="..\..\ExtensionsTools\ExtensionsTools.csproj" />
```

### 2. 添加必要的using语句

```csharp
using ExtensionsTools.ETOAAutomation;
using ExtensionsTools.ETOAAutomation.Models;
using ExtensionsTools.ETOAAutomation.Helpers;
using ExtensionsTools;
```

### 3. 配置文件设置

创建配置文件 `config.ini`：

```ini
[Server]
OAUrl=https://your-oa-server.com
ApiBaseUrl=https://your-oa-server.com/api

[Login]
TimeoutSeconds=30
MaxRetryAttempts=3
AutoLogin=true

[Session]
HeartbeatInterval=300
ExpiryHours=8
AutoRelogin=true

[Upload]
MaxFileSizeMB=100
AllowedExtensions=.pdf,.doc,.docx,.xls,.xlsx,.jpg,.png
ChunkSizeMB=5
MaxConcurrentUploads=3

[Browser]
UserAgent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
TimeoutSeconds=60
EnableJavaScript=true

[Security]
EncryptStorage=true
EncryptionKey=your-encryption-key

[Logging]
Level=Info
EnableFileLogging=true
LogRetentionDays=30
```

## 🚀 快速开始

### 1. 基本初始化

```csharp
public partial class MainForm : Form
{
    private ETOAClient _oaClient;
    
    public MainForm()
    {
        InitializeComponent();
        InitializeOAClient();
    }
    
    private void InitializeOAClient()
    {
        try
        {
            // 创建OA客户端实例
            _oaClient = new ETOAClient();
            
            // 配置事件处理
            _oaClient.LoginCompleted += OnLoginCompleted;
            _oaClient.SessionExpired += OnSessionExpired;
            _oaClient.UploadProgress += OnUploadProgress;
            
            ETLogManager.Info("ETOAClient初始化成功");
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"ETOAClient初始化失败: {ex.Message}", ex);
            MessageBox.Show($"初始化失败: {ex.Message}", "错误", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
```

### 2. 用户登录

```csharp
private async void btnLogin_Click(object sender, EventArgs e)
{
    try
    {
        // 创建登录信息
        var loginInfo = new ETOALoginInfo
        {
            Username = txtUsername.Text,
            Password = txtPassword.Text,
            LoginUrl = ETOAConfigHelper.GetOAServerUrl(),
            RememberMe = chkRememberMe.Checked
        };
        
        // 验证登录信息
        if (!loginInfo.Validate())
        {
            MessageBox.Show("请检查用户名和密码", "验证失败", 
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }
        
        // 显示登录进度
        progressBar.Visible = true;
        btnLogin.Enabled = false;
        lblStatus.Text = "正在登录...";
        
        // 执行登录
        var result = await _oaClient.LoginAsync(loginInfo);
        
        if (result.IsSuccess)
        {
            lblStatus.Text = "登录成功";
            // 启动会话监控
            _oaClient.StartSessionMonitoring();
            
            // 跳转到主界面
            ShowMainInterface();
        }
        else
        {
            lblStatus.Text = "登录失败";
            MessageBox.Show($"登录失败: {result.ErrorMessage}", "错误", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"登录异常: {ex.Message}", ex);
        MessageBox.Show($"登录异常: {ex.Message}", "异常", 
            MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
    finally
    {
        progressBar.Visible = false;
        btnLogin.Enabled = true;
    }
}
```

### 3. 文件上传

```csharp
private async void btnUpload_Click(object sender, EventArgs e)
{
    try
    {
        // 选择文件
        using (var openFileDialog = new OpenFileDialog())
        {
            openFileDialog.Filter = "所有支持的文件|*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.jpg;*.png";
            openFileDialog.Multiselect = true;
            
            if (openFileDialog.ShowDialog() != DialogResult.OK)
                return;
            
            var filePaths = openFileDialog.FileNames.ToList();
            
            // 显示上传进度
            progressBar.Maximum = 100;
            progressBar.Value = 0;
            progressBar.Visible = true;
            lblStatus.Text = "准备上传...";
            
            // 执行批量上传
            var uploadEndpoint = "/api/files/upload";
            var results = await _oaClient.UploadFilesAsync(uploadEndpoint, filePaths);
            
            // 处理上传结果
            var successCount = results.Count(r => r.IsSuccess);
            var failureCount = results.Count - successCount;
            
            lblStatus.Text = $"上传完成: 成功 {successCount}, 失败 {failureCount}";
            
            // 显示详细结果
            ShowUploadResults(results);
        }
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"文件上传异常: {ex.Message}", ex);
        MessageBox.Show($"上传异常: {ex.Message}", "异常", 
            MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
    finally
    {
        progressBar.Visible = false;
    }
}
```

### 4. API调用

```csharp
private async void btnCallApi_Click(object sender, EventArgs e)
{
    try
    {
        // 创建API请求
        var request = new ETOAApiRequest
        {
            Endpoint = "/api/users/profile",
            Method = HttpMethod.Get,
            RequireAuth = true,
            TimeoutSeconds = 30
        };
        
        // 添加请求头
        request.AddHeader("Accept", "application/json");
        
        // 执行API调用
        var response = await _oaClient.CallApiAsync<UserProfile>(request);
        
        if (response.IsSuccess)
        {
            var userProfile = response.GetData<UserProfile>();
            DisplayUserProfile(userProfile);
        }
        else
        {
            MessageBox.Show($"API调用失败: {response.ErrorMessage}", "错误", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"API调用异常: {ex.Message}", ex);
        MessageBox.Show($"API调用异常: {ex.Message}", "异常", 
            MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
```

## 🎯 高级功能

### 1. 自定义浏览器操作

```csharp
private async void btnCustomBrowserOperation_Click(object sender, EventArgs e)
{
    try
    {
        // 获取模拟浏览器实例
        var browser = _oaClient.GetSimulationBrowser();
        
        // 导航到目标页面
        await browser.NavigateAsync("https://your-oa-server.com/custom-page");
        
        // 等待页面加载
        await browser.WaitForPageLoadAsync(30);
        
        // 填充表单
        await browser.FillTextAsync("#username", "your-username");
        await browser.FillTextAsync("#password", "your-password");
        
        // 点击按钮
        await browser.ClickElementAsync("#login-button");
        
        // 等待操作完成
        await Task.Delay(2000);
        
        // 截图保存
        var screenshot = await browser.TakeScreenshotAsync();
        SaveScreenshot(screenshot);
        
        ETLogManager.Info("自定义浏览器操作完成");
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"浏览器操作异常: {ex.Message}", ex);
    }
}
```

### 2. 会话状态监控

```csharp
private void SetupSessionMonitoring()
{
    // 监听会话状态变化
    _oaClient.SessionStatusChanged += (sender, args) =>
    {
        Invoke(new Action(() =>
        {
            switch (args.Status)
            {
                case SessionStatus.Active:
                    lblSessionStatus.Text = "会话活跃";
                    lblSessionStatus.ForeColor = Color.Green;
                    break;
                    
                case SessionStatus.Warning:
                    lblSessionStatus.Text = "会话警告";
                    lblSessionStatus.ForeColor = Color.Orange;
                    break;
                    
                case SessionStatus.Expired:
                    lblSessionStatus.Text = "会话过期";
                    lblSessionStatus.ForeColor = Color.Red;
                    ShowLoginDialog();
                    break;
            }
        }));
    };
    
    // 监听心跳失败
    _oaClient.HeartbeatFailed += (sender, args) =>
    {
        ETLogManager.Warning($"心跳失败: {args.ErrorMessage}");
    };
    
    // 监听自动重登完成
    _oaClient.AutoReloginCompleted += (sender, args) =>
    {
        if (args.IsSuccess)
        {
            ETLogManager.Info("自动重登成功");
            Invoke(new Action(() =>
            {
                lblStatus.Text = "自动重登成功";
            }));
        }
        else
        {
            ETLogManager.Error($"自动重登失败: {args.ErrorMessage}");
        }
    };
}
```

### 3. 性能监控

```csharp
private void EnablePerformanceMonitoring()
{
    // 启动性能监控
    var timerId = ETOAPerformanceHelper.StartTimer("FileUpload");
    
    // 执行业务操作
    // ... 业务代码 ...
    
    // 停止性能监控
    var duration = ETOAPerformanceHelper.StopTimer(timerId);
    
    // 记录性能指标
    ETOAPerformanceHelper.RecordMetric("UploadSpeed", 1024 * 1024 / duration.TotalSeconds, "bytes/sec");
    
    // 获取性能摘要
    var summary = ETOAPerformanceHelper.GetMetricSummary("FileUpload");
    ETLogManager.Info($"上传性能: 平均时间 {summary.AverageTime.TotalSeconds:F2}秒");
}
```

## 🔧 配置管理

### 1. 动态配置更新

```csharp
private void UpdateConfiguration()
{
    // 更新服务器URL
    ETOAConfigHelper.UpdateConfig("Server.OAUrl", "https://new-server.com");
    
    // 更新超时设置
    ETOAConfigHelper.UpdateConfig("Login.TimeoutSeconds", 60);
    
    // 更新文件上传限制
    ETOAConfigHelper.UpdateConfig("Upload.MaxFileSizeMB", 200);
    
    // 重新加载配置
    ETOAConfigHelper.ReloadConfig();
    
    ETLogManager.Info("配置已更新");
}
```

### 2. 配置验证

```csharp
private bool ValidateConfiguration()
{
    try
    {
        // 验证服务器URL
        var serverUrl = ETOAConfigHelper.GetOAServerUrl();
        if (!Uri.TryCreate(serverUrl, UriKind.Absolute, out _))
        {
            ETLogManager.Error($"无效的服务器URL: {serverUrl}");
            return false;
        }
        
        // 验证超时设置
        var timeout = ETOAConfigHelper.GetLoginTimeout();
        if (timeout <= 0 || timeout > 300)
        {
            ETLogManager.Error($"无效的超时设置: {timeout}");
            return false;
        }
        
        // 验证文件大小限制
        var maxFileSize = ETOAConfigHelper.GetMaxFileSize();
        if (maxFileSize <= 0 || maxFileSize > 1024)
        {
            ETLogManager.Error($"无效的文件大小限制: {maxFileSize}MB");
            return false;
        }
        
        return true;
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"配置验证异常: {ex.Message}", ex);
        return false;
    }
}
```

## 🎯 事件处理

### 1. 登录事件

```csharp
private void OnLoginCompleted(object sender, LoginCompletedEventArgs e)
{
    if (e.IsSuccess)
    {
        ETLogManager.Info($"用户登录成功: {e.Username}");
        
        // 更新UI状态
        Invoke(new Action(() =>
        {
            lblWelcome.Text = $"欢迎, {e.Username}";
            btnLogin.Text = "注销";
            EnableMainFeatures(true);
        }));
        
        // 保存登录状态
        if (e.RememberMe)
        {
            SaveLoginCredentials(e.LoginInfo);
        }
    }
    else
    {
        ETLogManager.Warning($"用户登录失败: {e.ErrorMessage}");
        
        Invoke(new Action(() =>
        {
            MessageBox.Show($"登录失败: {e.ErrorMessage}", "登录失败", 
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }));
    }
}
```

### 2. 上传进度事件

```csharp
private void OnUploadProgress(object sender, UploadProgressEventArgs e)
{
    // 更新进度条
    Invoke(new Action(() =>
    {
        progressBar.Value = (int)e.Progress;
        lblProgress.Text = $"{e.Progress:F1}% ({e.UploadedSize}/{e.TotalSize} 字节)";
        
        // 显示当前文件信息
        if (!string.IsNullOrEmpty(e.FileName))
        {
            lblCurrentFile.Text = $"正在上传: {e.FileName}";
        }
        
        // 显示上传速度
        if (e.UploadSpeed > 0)
        {
            lblSpeed.Text = $"速度: {FormatFileSize(e.UploadSpeed)}/秒";
        }
        
        // 显示预计剩余时间
        if (e.EstimatedTimeRemaining.HasValue)
        {
            lblTimeRemaining.Text = $"剩余时间: {e.EstimatedTimeRemaining.Value:mm\\:ss}";
        }
    }));
}
```

## 🐛 错误处理

### 1. 全局异常处理

```csharp
private void SetupGlobalExceptionHandling()
{
    // 设置全局异常处理器
    Application.ThreadException += (sender, e) =>
    {
        ETLogManager.Error($"未处理的线程异常: {e.Exception.Message}", e.Exception);
        ShowErrorDialog("应用程序错误", e.Exception.Message);
    };
    
    AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
    {
        var exception = e.ExceptionObject as Exception;
        ETLogManager.Fatal($"未处理的应用程序域异常: {exception?.Message}", exception);
        ShowErrorDialog("严重错误", exception?.Message ?? "未知错误");
    };
}
```

### 2. 网络错误处理

```csharp
private async Task<bool> HandleNetworkError(Exception ex)
{
    if (ex is HttpRequestException || ex is TaskCanceledException)
    {
        ETLogManager.Warning($"网络错误: {ex.Message}");
        
        // 检查网络连接
        if (!await CheckNetworkConnectivity())
        {
            MessageBox.Show("网络连接不可用，请检查网络设置", "网络错误", 
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return false;
        }
        
        // 尝试重新连接
        var result = MessageBox.Show("网络连接异常，是否重试？", "网络错误", 
            MessageBoxButtons.YesNo, MessageBoxIcon.Question);
        
        return result == DialogResult.Yes;
    }
    
    return false;
}
```

---

**📅 文档版本**: v1.0  
**🔄 最后更新**: 2024年12月  
**👨‍💻 维护团队**: ETOAAutomation开发组
