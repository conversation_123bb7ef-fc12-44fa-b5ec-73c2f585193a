# 🔄 ETOASessionManager 会话管理流程详解

## 📋 概述

ETOASessionManager负责维护OA系统的登录会话状态，通过心跳监控、自动重登、会话持久化等机制确保用户会话的连续性和有效性。

## 🏗️ 类结构图

```mermaid
classDiagram
    class ETOASessionManager {
        +ETOASessionData CurrentSession
        +Timer HeartbeatTimer
        +bool IsMonitoring
        +bool AutoReloginEnabled
        +int HeartbeatInterval
        +int MaxReloginAttempts
        
        +StartMonitoring(loginInfo) void
        +StopMonitoring() void
        +SendHeartbeat() Task~bool~
        +CheckSessionValidity() bool
        +TriggerAutoRelogin() Task~bool~
        +SaveSession(sessionData) void
        +LoadSession(sessionId) ETOASessionData
        
        +event SessionStatusChanged
        +event HeartbeatFailed
        +event AutoReloginCompleted
        
        -OnHeartbeatTimer(sender, args) void
        -HandleSessionExpired() void
        -PerformAutoRelogin() Task~bool~
        -UpdateSessionActivity() void
    }
    
    ETOASessionManager --> ETOASessionData
    ETOASessionManager --> ETOASessionStorage
    ETOASessionManager --> ETOAAutoReloginHelper
    ETOASessionManager --> ETOAApiClient
```

## 🚀 会话监控启动流程

```mermaid
sequenceDiagram
    participant Client as ETOAClient
    participant Manager as ETOASessionManager
    participant Storage as ETOASessionStorage
    participant Timer as HeartbeatTimer
    participant Api as ETOAApiClient
    participant Log as ETLogManager
    
    Client->>Manager: StartMonitoring(loginInfo)
    Manager->>Manager: 验证登录信息
    
    alt 登录信息有效
        Manager->>Manager: 创建ETOASessionData
        Manager->>Storage: SaveSession(sessionData)
        Storage-->>Manager: 会话保存成功
        
        Manager->>Timer: 创建心跳定时器
        Timer-->>Manager: 定时器创建完成
        
        Manager->>Timer: 启动定时器
        Timer-->>Manager: 定时器已启动
        
        Manager->>Api: 设置会话信息
        Api-->>Manager: 会话信息已设置
        
        Manager->>Log: 记录监控启动日志
        Manager-->>Client: 监控启动成功
        
        loop 心跳循环
            Timer->>Manager: OnHeartbeatTimer触发
            Manager->>Manager: SendHeartbeat()
            Manager->>Api: 发送心跳请求
            Api-->>Manager: 心跳响应
            
            alt 心跳成功
                Manager->>Manager: UpdateSessionActivity()
                Manager->>Storage: 更新会话数据
            else 心跳失败
                Manager->>Manager: 处理心跳失败
                Manager->>Manager: 检查是否需要自动重登
            end
        end
    else 登录信息无效
        Manager-->>Client: 返回验证失败
    end
```

## 💓 心跳监控机制

```mermaid
flowchart TD
    A[心跳定时器触发] --> B[检查当前会话状态]
    B --> C{会话是否有效}
    C -->|无效| D[停止心跳监控]
    C -->|有效| E[构建心跳请求]
    E --> F[发送心跳到服务器]
    F --> G[等待服务器响应]
    G --> H{响应是否成功}
    H -->|成功| I[更新最后活动时间]
    H -->|失败| J[增加失败计数]
    I --> K[重置失败计数]
    K --> L[触发会话状态更新事件]
    L --> M[等待下次心跳]
    J --> N{失败次数是否超限}
    N -->|否| O[记录失败日志]
    N -->|是| P{是否启用自动重登}
    O --> M
    P -->|否| Q[触发会话失效事件]
    P -->|是| R[执行自动重登]
    R --> S{自动重登是否成功}
    S -->|成功| T[更新会话信息]
    S -->|失败| U[增加重登失败次数]
    T --> K
    U --> V{重登次数是否超限}
    V -->|否| W[等待下次重登尝试]
    V -->|是| Q
    W --> X[延迟后重新尝试]
    X --> R
    Q --> Y[清理会话数据]
    Y --> Z[停止监控]
    D --> Z
    
    style A fill:#e1f5fe
    style M fill:#e8f5e8
    style Q fill:#ffcdd2
    style T fill:#c8e6c9
    style Z fill:#f5f5f5
```

### 心跳实现代码

```csharp
public async Task<bool> SendHeartbeat()
{
    if (CurrentSession == null || !CurrentSession.IsValid)
    {
        ETLogManager.Warning("会话无效，跳过心跳检测");
        return false;
    }
    
    try
    {
        ETLogManager.Debug($"发送心跳请求，会话ID: {CurrentSession.SessionId}");
        
        // 1. 构建心跳请求
        var heartbeatEndpoint = ETOAConfigHelper.GetHeartbeatEndpoint();
        var heartbeatData = new
        {
            sessionId = CurrentSession.SessionId,
            userId = CurrentSession.UserId,
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
        };
        
        // 2. 发送心跳请求
        var response = await ApiClient.PostAsync<dynamic>(heartbeatEndpoint, heartbeatData, 1);
        
        if (response.IsSuccess)
        {
            // 3. 心跳成功处理
            CurrentSession.LastHeartbeatTime = DateTime.Now;
            CurrentSession.LastActivityTime = DateTime.Now;
            HeartbeatFailureCount = 0;
            
            // 4. 保存会话状态
            SessionStorage.SaveSession(CurrentSession);
            
            // 5. 触发状态更新事件
            OnSessionStatusChanged?.Invoke(new SessionStatusChangedEventArgs
            {
                SessionId = CurrentSession.SessionId,
                Status = SessionStatus.Active,
                LastActivity = CurrentSession.LastActivityTime
            });
            
            ETLogManager.Debug("心跳检测成功");
            return true;
        }
        else
        {
            // 6. 心跳失败处理
            HeartbeatFailureCount++;
            ETLogManager.Warning($"心跳检测失败: {response.ErrorMessage}，失败次数: {HeartbeatFailureCount}");
            
            // 7. 触发心跳失败事件
            OnHeartbeatFailed?.Invoke(new HeartbeatFailedEventArgs
            {
                SessionId = CurrentSession.SessionId,
                FailureCount = HeartbeatFailureCount,
                ErrorMessage = response.ErrorMessage
            });
            
            return false;
        }
    }
    catch (Exception ex)
    {
        HeartbeatFailureCount++;
        ETLogManager.Error($"心跳检测异常: {ex.Message}，失败次数: {HeartbeatFailureCount}", ex);
        
        OnHeartbeatFailed?.Invoke(new HeartbeatFailedEventArgs
        {
            SessionId = CurrentSession?.SessionId,
            FailureCount = HeartbeatFailureCount,
            ErrorMessage = ex.Message,
            Exception = ex
        });
        
        return false;
    }
}
```

## 🔄 自动重登机制

```mermaid
sequenceDiagram
    participant Manager as ETOASessionManager
    participant Helper as ETOAAutoReloginHelper
    participant Browser as ETOALoginBrowser
    participant Storage as ETOASessionStorage
    participant Api as ETOAApiClient
    participant Log as ETLogManager
    
    Manager->>Manager: 检测到会话失效
    Manager->>Helper: TriggerAutoRelogin()
    Helper->>Helper: 检查重登条件
    
    alt 满足重登条件
        Helper->>Storage: 获取存储的登录凭据
        Storage-->>Helper: 返回用户名和密码
        
        Helper->>Browser: 创建登录浏览器实例
        Browser-->>Helper: 浏览器创建完成
        
        Helper->>Browser: AutoLoginAsync(username, password)
        Browser->>Browser: 执行自动登录流程
        Browser-->>Helper: 返回登录结果
        
        alt 登录成功
            Helper->>Helper: 提取新的认证信息
            Helper->>Manager: 更新会话数据
            Manager->>Storage: 保存新会话
            Manager->>Api: 更新API客户端认证
            
            Manager->>Log: 记录重登成功日志
            Manager->>Manager: 重置失败计数
            Manager->>Manager: 触发重登完成事件
            Manager-->>Manager: 继续会话监控
        else 登录失败
            Helper->>Log: 记录重登失败日志
            Helper->>Manager: 增加重登失败次数
            Manager->>Manager: 检查重登次数限制
            
            alt 未超过重登次数限制
                Manager->>Manager: 等待下次重登尝试
            else 超过重登次数限制
                Manager->>Manager: 标记会话彻底失效
                Manager->>Manager: 停止会话监控
                Manager->>Manager: 触发会话失效事件
            end
        end
    else 不满足重登条件
        Helper-->>Manager: 返回重登条件不满足
        Manager->>Manager: 停止会话监控
        Manager->>Manager: 触发会话失效事件
    end
```

### 自动重登实现

```csharp
public async Task<bool> TriggerAutoRelogin()
{
    if (!AutoReloginEnabled)
    {
        ETLogManager.Info("自动重登功能已禁用");
        return false;
    }
    
    if (AutoReloginAttempts >= MaxReloginAttempts)
    {
        ETLogManager.Warning($"自动重登次数已达上限 {MaxReloginAttempts}，停止重登尝试");
        return false;
    }
    
    try
    {
        AutoReloginAttempts++;
        ETLogManager.Info($"开始第 {AutoReloginAttempts} 次自动重登尝试");
        
        // 1. 使用自动重登助手
        var reloginResult = await AutoReloginHelper.AttemptReloginAsync();
        
        if (reloginResult.IsSuccess)
        {
            // 2. 重登成功，更新会话信息
            var newSessionData = new ETOASessionData
            {
                SessionId = Guid.NewGuid().ToString(),
                UserId = reloginResult.UserId,
                Username = reloginResult.Username,
                AuthToken = reloginResult.Token,
                Cookies = reloginResult.Cookies,
                CreatedTime = DateTime.Now,
                LastActivityTime = DateTime.Now,
                LastHeartbeatTime = DateTime.Now,
                ExpiryTime = DateTime.Now.AddHours(ETOAConfigHelper.GetSessionExpiryHours()),
                Status = SessionStatus.Active
            };
            
            // 3. 保存新会话
            CurrentSession = newSessionData;
            SessionStorage.SaveSession(CurrentSession);
            
            // 4. 更新API客户端认证信息
            ApiClient.SetAuthenticationInfo(reloginResult);
            
            // 5. 重置失败计数
            HeartbeatFailureCount = 0;
            AutoReloginAttempts = 0;
            
            // 6. 触发重登完成事件
            OnAutoReloginCompleted?.Invoke(new AutoReloginCompletedEventArgs
            {
                IsSuccess = true,
                NewSessionId = CurrentSession.SessionId,
                ReloginTime = DateTime.Now,
                AttemptCount = AutoReloginAttempts
            });
            
            ETLogManager.Info("自动重登成功");
            return true;
        }
        else
        {
            // 7. 重登失败处理
            ETLogManager.Warning($"自动重登失败: {reloginResult.ErrorMessage}");
            
            // 8. 检查是否还有重试机会
            if (AutoReloginAttempts < MaxReloginAttempts)
            {
                var retryDelay = TimeSpan.FromMinutes(Math.Pow(2, AutoReloginAttempts)); // 指数退避
                ETLogManager.Info($"将在 {retryDelay.TotalMinutes} 分钟后进行下次重登尝试");
                
                // 设置延迟重试
                _ = Task.Delay(retryDelay).ContinueWith(async _ =>
                {
                    await TriggerAutoRelogin();
                });
            }
            else
            {
                // 9. 重登次数耗尽，触发会话失效
                HandleSessionExpired();
            }
            
            return false;
        }
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"自动重登过程发生异常: {ex.Message}", ex);
        
        OnAutoReloginCompleted?.Invoke(new AutoReloginCompletedEventArgs
        {
            IsSuccess = false,
            ErrorMessage = ex.Message,
            Exception = ex,
            ReloginTime = DateTime.Now,
            AttemptCount = AutoReloginAttempts
        });
        
        return false;
    }
}
```

## 💾 会话持久化机制

```mermaid
graph TD
    A[会话数据变更] --> B[检查是否需要持久化]
    B --> C{持久化条件}
    C -->|会话创建| D[保存新会话]
    C -->|会话更新| E[更新现有会话]
    C -->|会话失效| F[删除会话数据]
    C -->|定期备份| G[备份会话数据]
    
    D --> H[加密敏感信息]
    E --> H
    G --> H
    
    H --> I[序列化会话对象]
    I --> J[写入存储文件]
    J --> K[验证写入结果]
    K --> L{写入是否成功}
    L -->|成功| M[更新内存缓存]
    L -->|失败| N[记录存储错误]
    
    F --> O[清理存储文件]
    O --> P[清理内存缓存]
    
    M --> Q[触发持久化完成事件]
    N --> R[触发持久化失败事件]
    P --> S[触发会话清理事件]
    
    style A fill:#e1f5fe
    style Q fill:#c8e6c9
    style R fill:#ffcdd2
    style S fill:#f5f5f5
```

### 会话存储实现

```csharp
public class ETOASessionStorage
{
    private readonly string StoragePath;
    private readonly ETOAStorageHelper StorageHelper;
    
    public ETOASessionStorage()
    {
        StoragePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
            "ETOAAutomation", "Sessions"
        );
        
        Directory.CreateDirectory(StoragePath);
        StorageHelper = new ETOAStorageHelper();
    }
    
    public void SaveSession(ETOASessionData sessionData)
    {
        try
        {
            if (sessionData == null)
                throw new ArgumentNullException(nameof(sessionData));
            
            // 1. 加密敏感信息
            var encryptedSession = EncryptSensitiveData(sessionData);
            
            // 2. 序列化会话数据
            var sessionJson = ETOAJsonHelper.ToJson(encryptedSession, true);
            
            // 3. 保存到文件
            var fileName = $"session_{sessionData.SessionId}.json";
            var filePath = Path.Combine(StoragePath, fileName);
            
            StorageHelper.SaveData(filePath, sessionJson, false);
            
            // 4. 更新会话索引
            UpdateSessionIndex(sessionData.SessionId, sessionData.Username, DateTime.Now);
            
            ETLogManager.Debug($"会话已保存: {sessionData.SessionId}");
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"保存会话数据失败: {ex.Message}", ex);
            throw;
        }
    }
    
    public ETOASessionData LoadSession(string sessionId)
    {
        try
        {
            if (string.IsNullOrEmpty(sessionId))
                return null;
            
            var fileName = $"session_{sessionId}.json";
            var filePath = Path.Combine(StoragePath, fileName);
            
            if (!File.Exists(filePath))
                return null;
            
            // 1. 读取会话数据
            var sessionJson = StorageHelper.LoadData<string>(filePath, false);
            
            // 2. 反序列化
            var encryptedSession = ETOAJsonHelper.FromJson<ETOASessionData>(sessionJson);
            
            // 3. 解密敏感信息
            var sessionData = DecryptSensitiveData(encryptedSession);
            
            // 4. 验证会话有效性
            if (sessionData.IsExpired)
            {
                ETLogManager.Info($"会话已过期: {sessionId}");
                DeleteSession(sessionId);
                return null;
            }
            
            ETLogManager.Debug($"会话已加载: {sessionId}");
            return sessionData;
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"加载会话数据失败: {ex.Message}", ex);
            return null;
        }
    }
    
    private ETOASessionData EncryptSensitiveData(ETOASessionData session)
    {
        var encrypted = session.Clone();
        
        // 加密认证Token
        if (!string.IsNullOrEmpty(encrypted.AuthToken))
        {
            encrypted.AuthToken = StorageHelper.EncryptData(encrypted.AuthToken, "session_token");
        }
        
        // 加密Cookie信息
        if (encrypted.Cookies != null && encrypted.Cookies.Count > 0)
        {
            var cookiesJson = ETOAJsonHelper.ToJson(encrypted.Cookies);
            encrypted.EncryptedCookies = StorageHelper.EncryptData(cookiesJson, "session_cookies");
            encrypted.Cookies = null; // 清除明文Cookie
        }
        
        return encrypted;
    }
}
```

## 📊 会话状态监控

```mermaid
graph LR
    A[会话状态变更] --> B[状态分类]
    B --> C[Active活跃]
    B --> D[Idle空闲]
    B --> E[Warning警告]
    B --> F[Expired过期]
    B --> G[Invalid无效]
    
    C --> H[正常监控]
    D --> I[降低监控频率]
    E --> J[增加监控频率]
    F --> K[停止监控]
    G --> L[清理会话]
    
    H --> M[记录活跃日志]
    I --> N[记录空闲日志]
    J --> O[记录警告日志]
    K --> P[记录过期日志]
    L --> Q[记录清理日志]
    
    M --> R[更新统计数据]
    N --> R
    O --> R
    P --> R
    Q --> R
```

### 会话状态枚举

```csharp
public enum SessionStatus
{
    /// <summary>
    /// 活跃状态 - 正常使用中
    /// </summary>
    Active,
    
    /// <summary>
    /// 空闲状态 - 长时间无活动
    /// </summary>
    Idle,
    
    /// <summary>
    /// 警告状态 - 即将过期或出现问题
    /// </summary>
    Warning,
    
    /// <summary>
    /// 过期状态 - 会话已过期
    /// </summary>
    Expired,
    
    /// <summary>
    /// 无效状态 - 会话数据损坏或无效
    /// </summary>
    Invalid,
    
    /// <summary>
    /// 重登中状态 - 正在执行自动重登
    /// </summary>
    Relogging
}
```

---

**📅 文档版本**: v1.0  
**🔄 最后更新**: 2024年12月  
**👨‍💻 维护团队**: ETOAAutomation开发组
