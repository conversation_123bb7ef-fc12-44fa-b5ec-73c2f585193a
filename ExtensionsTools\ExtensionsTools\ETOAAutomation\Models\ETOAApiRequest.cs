using System;
using System.Collections.Generic;
using System.Linq;

namespace ET.ETOAAutomation.Models
{
    /// <summary>
    /// API请求模型
    /// </summary>
    public class ETOAApiRequest
    {
        /// <summary>
        /// 请求ID（用于追踪）
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// HTTP方法（GET、POST、PUT、DELETE等）
        /// </summary>
        public string Method { get; set; }

        /// <summary>
        /// API端点URL
        /// </summary>
        public string Endpoint { get; set; }

        /// <summary>
        /// 完整的请求URL
        /// </summary>
        public string FullUrl { get; set; }

        /// <summary>
        /// 请求头信息
        /// </summary>
        public Dictionary<string, string> Headers { get; set; }

        /// <summary>
        /// 查询参数
        /// </summary>
        public Dictionary<string, string> QueryParameters { get; set; }

        /// <summary>
        /// 请求体数据
        /// </summary>
        public object Body { get; set; }

        /// <summary>
        /// 请求体JSON字符串
        /// </summary>
        public string BodyJson { get; set; }

        /// <summary>
        /// 表单数据
        /// </summary>
        public Dictionary<string, string> FormData { get; set; }

        /// <summary>
        /// 文件数据
        /// </summary>
        public Dictionary<string, byte[]> FileData { get; set; }

        /// <summary>
        /// 内容类型
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// 是否需要认证
        /// </summary>
        public bool RequireAuthentication { get; set; }

        /// <summary>
        /// 认证Token
        /// </summary>
        public string AuthToken { get; set; }

        /// <summary>
        /// Cookie信息
        /// </summary>
        public Dictionary<string, string> Cookies { get; set; }

        /// <summary>
        /// 请求创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 请求发送时间
        /// </summary>
        public DateTime? SentTime { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ETOAApiRequest()
        {
            RequestId = Guid.NewGuid().ToString();
            Headers = new Dictionary<string, string>();
            QueryParameters = new Dictionary<string, string>();
            FormData = new Dictionary<string, string>();
            FileData = new Dictionary<string, byte[]>();
            Cookies = new Dictionary<string, string>();
            ExtendedProperties = new Dictionary<string, object>();
            CreatedTime = DateTime.Now;
            TimeoutSeconds = 30;
            RetryCount = 3;
            RequireAuthentication = true;
            ContentType = "application/json";
        }

        /// <summary>
        /// 构造函数（指定方法和端点）
        /// </summary>
        /// <param name="method">HTTP方法</param>
        /// <param name="endpoint">API端点</param>
        public ETOAApiRequest(string method, string endpoint) : this()
        {
            Method = method?.ToUpper();
            Endpoint = endpoint;
        }

        /// <summary>
        /// 添加请求头
        /// </summary>
        /// <param name="name">请求头名称</param>
        /// <param name="value">请求头值</param>
        public void AddHeader(string name, string value)
        {
            if (Headers.ContainsKey(name))
            {
                Headers[name] = value;
            }
            else
            {
                Headers.Add(name, value);
            }
        }

        /// <summary>
        /// 添加查询参数
        /// </summary>
        /// <param name="name">参数名称</param>
        /// <param name="value">参数值</param>
        public void AddQueryParameter(string name, string value)
        {
            if (QueryParameters.ContainsKey(name))
            {
                QueryParameters[name] = value;
            }
            else
            {
                QueryParameters.Add(name, value);
            }
        }

        /// <summary>
        /// 添加表单数据
        /// </summary>
        /// <param name="name">字段名称</param>
        /// <param name="value">字段值</param>
        public void AddFormData(string name, string value)
        {
            if (FormData.ContainsKey(name))
            {
                FormData[name] = value;
            }
            else
            {
                FormData.Add(name, value);
            }
        }

        /// <summary>
        /// 添加文件数据
        /// </summary>
        /// <param name="name">文件字段名称</param>
        /// <param name="fileData">文件数据</param>
        public void AddFileData(string name, byte[] fileData)
        {
            if (FileData.ContainsKey(name))
            {
                FileData[name] = fileData;
            }
            else
            {
                FileData.Add(name, fileData);
            }
        }

        /// <summary>
        /// 添加Cookie
        /// </summary>
        /// <param name="name">Cookie名称</param>
        /// <param name="value">Cookie值</param>
        public void AddCookie(string name, string value)
        {
            if (Cookies.ContainsKey(name))
            {
                Cookies[name] = value;
            }
            else
            {
                Cookies.Add(name, value);
            }
        }

        /// <summary>
        /// 设置认证信息
        /// </summary>
        /// <param name="loginInfo">登录信息</param>
        public void SetAuthentication(ETOALoginInfo loginInfo)
        {
            if (loginInfo != null)
            {
                AuthToken = loginInfo.Token;

                // 复制Cookie信息
                foreach (var cookie in loginInfo.Cookies)
                {
                    AddCookie(cookie.Key, cookie.Value);
                }

                // 复制请求头信息
                foreach (var header in loginInfo.Headers)
                {
                    AddHeader(header.Key, header.Value);
                }
            }
        }

        /// <summary>
        /// 构建完整的请求URL
        /// </summary>
        /// <param name="baseUrl">基础URL</param>
        /// <returns>完整URL</returns>
        public string BuildFullUrl(string baseUrl)
        {
            var url = baseUrl.TrimEnd('/') + "/" + Endpoint.TrimStart('/');

            if (QueryParameters.Count > 0)
            {
                var queryString = string.Join("&",
                    QueryParameters.Select(kv => $"{Uri.EscapeDataString(kv.Key)}={Uri.EscapeDataString(kv.Value)}"));
                url += "?" + queryString;
            }

            FullUrl = url;
            return url;
        }

        /// <summary>
        /// 验证请求是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            if (string.IsNullOrEmpty(Method))
            {
                return (false, "HTTP方法不能为空");
            }

            if (string.IsNullOrEmpty(Endpoint))
            {
                return (false, "API端点不能为空");
            }

            if (RequireAuthentication && string.IsNullOrEmpty(AuthToken) && Cookies.Count == 0)
            {
                return (false, "需要认证但未提供认证信息");
            }

            return (true, null);
        }

        /// <summary>
        /// 获取扩展属性值
        /// </summary>
        /// <typeparam name="T">属性值类型</typeparam>
        /// <param name="propertyName">属性名称</param>
        /// <returns>属性值</returns>
        public T GetExtendedProperty<T>(string propertyName)
        {
            if (ExtendedProperties.ContainsKey(propertyName))
            {
                return (T)ExtendedProperties[propertyName];
            }
            return default(T);
        }

        /// <summary>
        /// 设置扩展属性值
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="propertyValue">属性值</param>
        public void SetExtendedProperty(string propertyName, object propertyValue)
        {
            if (ExtendedProperties.ContainsKey(propertyName))
            {
                ExtendedProperties[propertyName] = propertyValue;
            }
            else
            {
                ExtendedProperties.Add(propertyName, propertyValue);
            }
        }
    }
}