# 🛠️ ETOAHelper 辅助类流程详解

## 📋 概述

ETOAHelper辅助类集合提供了配置管理、JSON处理、<PERSON>ie管理、存储加密、重试机制、性能监控等核心功能，为ETOAAutomation模块提供全面的基础服务支持。

## 🏗️ 辅助类架构图

```mermaid
classDiagram
    class ETOAConfigHelper {
        +GetOAServerUrl() string
        +GetLoginTimeout() int
        +GetHeartbeatInterval() int
        +GetMaxRetryAttempts() int
        +GetSessionExpiryHours() int
        +GetMaxFileSize() int
        +GetAllowedFileExtensions() List~string~
        +GetChunkSize() int
        +GetMaxConcurrentUploads() int
        +UpdateConfig(key, value) void
        +ReloadConfig() void
        
        -ConfigPath string
        -ConfigCache Dictionary~string,object~
        -LastModified DateTime
        -LoadConfigFromFile() void
        -ValidateConfigValue(key, value) bool
    }
    
    class ETOAJsonHelper {
        +ToJson(obj, formatted) string
        +FromJson~T~(json) T
        +TryParseJson~T~(json, result) bool
        +ValidateJsonSchema(json, schema) bool
        +MergeJsonObjects(json1, json2) string
        +ExtractJsonValue(json, path) object
        +CompressJson(json) string
        +DecompressJson(compressed) string
        
        -JsonSettings JsonSerializerSettings
        -CreateDefaultSettings() JsonSerializerSettings
        -HandleSerializationError(error) void
    }
    
    class ETOACookieHelper {
        +SaveCookies(cookies, filePath) void
        +LoadCookies(filePath) List~Cookie~
        +ConvertToNetCookies(cefCookies) List~Cookie~
        +ConvertToCefCookies(netCookies) List~CefCookie~
        +FilterExpiredCookies(cookies) List~Cookie~
        +MergeCookieCollections(cookies1, cookies2) List~Cookie~
        +GetCookieValue(cookies, name) string
        +SetCookieValue(cookies, name, value) void
        
        -EncryptCookieData(data) string
        -DecryptCookieData(encrypted) string
        -ValidateCookieFormat(cookie) bool
    }
    
    class ETOAStorageHelper {
        +SaveData~T~(filePath, data, encrypt) void
        +LoadData~T~(filePath, decrypt) T
        +EncryptData(data, key) string
        +DecryptData(encrypted, key) string
        +CompressData(data) byte[]
        +DecompressData(compressed) byte[]
        +CreateBackup(filePath) string
        +RestoreFromBackup(backupPath, targetPath) void
        
        -EncryptionKey string
        -CompressionLevel CompressionLevel
        -GenerateKey(seed) string
        -ValidateDataIntegrity(data, hash) bool
    }
    
    class ETOARetryHelper {
        +ExecuteWithRetryAsync~T~(operation, maxAttempts) Task~T~
        +ExecuteWithExponentialBackoff~T~(operation, maxAttempts) Task~T~
        +CreateRetryPolicy(maxAttempts, delay) RetryPolicy
        +ShouldRetry(exception, attemptCount) bool
        +CalculateDelay(attemptCount, baseDelay) TimeSpan
        +LogRetryAttempt(attemptCount, exception) void
        
        -RetryableExceptions List~Type~
        -MaxDelaySeconds int
        -BackoffMultiplier double
        -IsRetryableException(exception) bool
    }
    
    class ETOAPerformanceHelper {
        +StartTimer(operationName) string
        +StopTimer(timerId) TimeSpan
        +RecordMetric(name, value, unit) void
        +GetAverageTime(operationName) TimeSpan
        +GetMetricSummary(name) MetricSummary
        +ExportMetrics(format) string
        +ClearMetrics() void
        +SetPerformanceThreshold(operation, threshold) void
        
        -Timers Dictionary~string,Stopwatch~
        -Metrics Dictionary~string,List~double~~
        -Thresholds Dictionary~string,TimeSpan~
        -CheckThreshold(operation, duration) void
    }
    
    ETOAConfigHelper --> ETIniFile
    ETOAJsonHelper --> Newtonsoft.Json
    ETOACookieHelper --> ETOAStorageHelper
    ETOAStorageHelper --> System.Security.Cryptography
    ETOARetryHelper --> ETLogManager
    ETOAPerformanceHelper --> ETLogManager
```

## ⚙️ 配置管理流程

```mermaid
sequenceDiagram
    participant Client as ETOAClient
    participant Config as ETOAConfigHelper
    participant IniFile as ETIniFile
    participant Cache as ConfigCache
    participant Log as ETLogManager
    
    Client->>Config: GetOAServerUrl()
    Config->>Config: 检查缓存是否有效
    
    alt 缓存有效
        Config->>Cache: 从缓存获取配置
        Cache-->>Config: 返回缓存值
        Config-->>Client: 返回配置值
    else 缓存无效或过期
        Config->>IniFile: 读取配置文件
        IniFile-->>Config: 返回配置数据
        
        Config->>Config: 验证配置值
        alt 配置值有效
            Config->>Cache: 更新缓存
            Config->>Log: 记录配置加载日志
            Config-->>Client: 返回配置值
        else 配置值无效
            Config->>Log: 记录配置错误
            Config->>Config: 使用默认值
            Config-->>Client: 返回默认值
        end
    end
    
    Note over Client,Log: 配置更新流程
    Client->>Config: UpdateConfig(key, value)
    Config->>Config: 验证新配置值
    
    alt 验证通过
        Config->>IniFile: 写入配置文件
        Config->>Cache: 更新缓存
        Config->>Log: 记录配置更新
        Config-->>Client: 返回更新成功
    else 验证失败
        Config->>Log: 记录验证失败
        Config-->>Client: 返回更新失败
    end
```

### 配置管理实现

```csharp
public class ETOAConfigHelper
{
    private static readonly string ConfigPath = Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
        "ETOAAutomation", "config.ini"
    );
    
    private static readonly Dictionary<string, object> ConfigCache = new Dictionary<string, object>();
    private static DateTime LastModified = DateTime.MinValue;
    private static readonly object LockObject = new object();
    
    public static string GetOAServerUrl()
    {
        return GetConfigValue("Server.OAUrl", "https://oa.company.com");
    }
    
    public static int GetLoginTimeout()
    {
        return GetConfigValue("Login.TimeoutSeconds", 30);
    }
    
    public static int GetHeartbeatInterval()
    {
        return GetConfigValue("Session.HeartbeatInterval", 300); // 5分钟
    }
    
    public static int GetMaxRetryAttempts()
    {
        return GetConfigValue("Network.MaxRetryAttempts", 3);
    }
    
    public static int GetSessionExpiryHours()
    {
        return GetConfigValue("Session.ExpiryHours", 8);
    }
    
    public static int GetMaxFileSize()
    {
        return GetConfigValue("Upload.MaxFileSizeMB", 100);
    }
    
    public static List<string> GetAllowedFileExtensions()
    {
        var extensions = GetConfigValue("Upload.AllowedExtensions", ".pdf,.doc,.docx,.xls,.xlsx,.jpg,.png");
        return extensions.Split(',').Select(ext => ext.Trim().ToLowerInvariant()).ToList();
    }
    
    private static T GetConfigValue<T>(string key, T defaultValue)
    {
        lock (LockObject)
        {
            try
            {
                // 检查配置文件是否有更新
                if (File.Exists(ConfigPath))
                {
                    var fileModified = File.GetLastWriteTime(ConfigPath);
                    if (fileModified > LastModified)
                    {
                        LoadConfigFromFile();
                        LastModified = fileModified;
                    }
                }
                else
                {
                    // 配置文件不存在，创建默认配置
                    CreateDefaultConfig();
                }
                
                // 从缓存获取配置值
                if (ConfigCache.TryGetValue(key, out var cachedValue))
                {
                    if (cachedValue is T typedValue)
                    {
                        return typedValue;
                    }
                    
                    // 尝试类型转换
                    try
                    {
                        return (T)Convert.ChangeType(cachedValue, typeof(T));
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning($"配置值类型转换失败: {key}, 值: {cachedValue}, 目标类型: {typeof(T)}, 错误: {ex.Message}");
                    }
                }
                
                // 使用默认值并保存到配置
                UpdateConfig(key, defaultValue);
                return defaultValue;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"获取配置值异常: {key}, 错误: {ex.Message}", ex);
                return defaultValue;
            }
        }
    }
    
    public static void UpdateConfig<T>(string key, T value)
    {
        lock (LockObject)
        {
            try
            {
                // 验证配置值
                if (!ValidateConfigValue(key, value))
                {
                    ETLogManager.Warning($"配置值验证失败: {key} = {value}");
                    return;
                }
                
                // 更新缓存
                ConfigCache[key] = value;
                
                // 写入配置文件
                var section = key.Contains('.') ? key.Split('.')[0] : "General";
                var keyName = key.Contains('.') ? key.Split('.')[1] : key;
                
                ETIniFile.WriteValue(ConfigPath, section, keyName, value?.ToString() ?? "");
                
                ETLogManager.Debug($"配置已更新: {key} = {value}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"更新配置异常: {key} = {value}, 错误: {ex.Message}", ex);
            }
        }
    }
    
    private static void LoadConfigFromFile()
    {
        try
        {
            ConfigCache.Clear();
            
            if (!File.Exists(ConfigPath))
                return;
            
            // 读取所有配置节
            var sections = ETIniFile.GetSections(ConfigPath);
            
            foreach (var section in sections)
            {
                var keys = ETIniFile.GetKeys(ConfigPath, section);
                
                foreach (var key in keys)
                {
                    var value = ETIniFile.ReadValue(ConfigPath, section, key, "");
                    var fullKey = $"{section}.{key}";
                    
                    // 尝试解析为适当的类型
                    if (int.TryParse(value, out var intValue))
                    {
                        ConfigCache[fullKey] = intValue;
                    }
                    else if (bool.TryParse(value, out var boolValue))
                    {
                        ConfigCache[fullKey] = boolValue;
                    }
                    else if (double.TryParse(value, out var doubleValue))
                    {
                        ConfigCache[fullKey] = doubleValue;
                    }
                    else
                    {
                        ConfigCache[fullKey] = value;
                    }
                }
            }
            
            ETLogManager.Debug($"配置文件已加载: {ConfigCache.Count} 个配置项");
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"加载配置文件异常: {ex.Message}", ex);
        }
    }
    
    private static bool ValidateConfigValue<T>(string key, T value)
    {
        try
        {
            switch (key)
            {
                case "Login.TimeoutSeconds":
                case "Session.HeartbeatInterval":
                case "Session.ExpiryHours":
                case "Network.MaxRetryAttempts":
                    return Convert.ToInt32(value) > 0;
                
                case "Upload.MaxFileSizeMB":
                    return Convert.ToInt32(value) > 0 && Convert.ToInt32(value) <= 1024;
                
                case "Server.OAUrl":
                    return Uri.TryCreate(value?.ToString(), UriKind.Absolute, out _);
                
                default:
                    return true; // 其他配置项不进行特殊验证
            }
        }
        catch
        {
            return false;
        }
    }
    
    private static void CreateDefaultConfig()
    {
        try
        {
            Directory.CreateDirectory(Path.GetDirectoryName(ConfigPath));
            
            // 创建默认配置
            var defaultConfigs = new Dictionary<string, object>
            {
                ["Server.OAUrl"] = "https://oa.company.com",
                ["Login.TimeoutSeconds"] = 30,
                ["Session.HeartbeatInterval"] = 300,
                ["Session.ExpiryHours"] = 8,
                ["Network.MaxRetryAttempts"] = 3,
                ["Upload.MaxFileSizeMB"] = 100,
                ["Upload.AllowedExtensions"] = ".pdf,.doc,.docx,.xls,.xlsx,.jpg,.png",
                ["Upload.ChunkSizeMB"] = 5,
                ["Upload.MaxConcurrentUploads"] = 3,
                ["Browser.UserAgent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                ["Browser.TimeoutSeconds"] = 60,
                ["Performance.EnableMonitoring"] = true,
                ["Performance.MetricRetentionDays"] = 7,
                ["Security.EncryptStorage"] = true,
                ["Logging.Level"] = "Info"
            };
            
            foreach (var config in defaultConfigs)
            {
                UpdateConfig(config.Key, config.Value);
            }
            
            ETLogManager.Info("默认配置文件已创建");
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"创建默认配置异常: {ex.Message}", ex);
        }
    }
}
```

## 📄 JSON处理流程

```mermaid
flowchart TD
    A[JSON操作请求] --> B{操作类型}
    B -->|序列化| C[对象转JSON]
    B -->|反序列化| D[JSON转对象]
    B -->|验证| E[JSON格式验证]
    B -->|合并| F[JSON对象合并]
    B -->|提取| G[JSON值提取]
    
    C --> H[配置序列化设置]
    H --> I[处理特殊类型]
    I --> J[执行序列化]
    J --> K{序列化是否成功}
    K -->|是| L[返回JSON字符串]
    K -->|否| M[记录序列化错误]
    
    D --> N[验证JSON格式]
    N --> O{格式是否有效}
    O -->|否| P[返回格式错误]
    O -->|是| Q[配置反序列化设置]
    Q --> R[执行反序列化]
    R --> S{反序列化是否成功}
    S -->|是| T[返回对象实例]
    S -->|否| U[记录反序列化错误]
    
    E --> V[解析JSON结构]
    V --> W[验证语法正确性]
    W --> X[检查数据类型]
    X --> Y[返回验证结果]
    
    F --> Z[解析多个JSON对象]
    Z --> AA[合并对象属性]
    AA --> BB[处理属性冲突]
    BB --> CC[生成合并结果]
    
    G --> DD[解析JSON路径]
    DD --> EE[导航到目标节点]
    EE --> FF[提取节点值]
    FF --> GG[返回提取结果]
    
    style A fill:#e1f5fe
    style L fill:#c8e6c9
    style T fill:#c8e6c9
    style Y fill:#c8e6c9
    style CC fill:#c8e6c9
    style GG fill:#c8e6c9
    style M fill:#ffcdd2
    style P fill:#ffcdd2
    style U fill:#ffcdd2
```

### JSON处理实现

```csharp
public class ETOAJsonHelper
{
    private static readonly JsonSerializerSettings DefaultSettings;
    
    static ETOAJsonHelper()
    {
        DefaultSettings = CreateDefaultSettings();
    }
    
    public static string ToJson(object obj, bool formatted = false)
    {
        try
        {
            if (obj == null)
                return "null";
            
            var settings = DefaultSettings;
            if (formatted)
            {
                settings = new JsonSerializerSettings(DefaultSettings)
                {
                    Formatting = Formatting.Indented
                };
            }
            
            var json = JsonConvert.SerializeObject(obj, settings);
            ETLogManager.Debug($"对象序列化成功: 类型={obj.GetType().Name}, 长度={json.Length}");
            
            return json;
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"JSON序列化异常: 类型={obj?.GetType().Name}, 错误={ex.Message}", ex);
            throw new ETOAJsonException($"JSON序列化失败: {ex.Message}", ex);
        }
    }
    
    public static T FromJson<T>(string json)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(json))
                return default(T);
            
            var result = JsonConvert.DeserializeObject<T>(json, DefaultSettings);
            ETLogManager.Debug($"JSON反序列化成功: 目标类型={typeof(T).Name}, JSON长度={json.Length}");
            
            return result;
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"JSON反序列化异常: 目标类型={typeof(T).Name}, JSON={json.Substring(0, Math.Min(100, json.Length))}, 错误={ex.Message}", ex);
            throw new ETOAJsonException($"JSON反序列化失败: {ex.Message}", ex);
        }
    }
    
    public static bool TryParseJson<T>(string json, out T result)
    {
        result = default(T);
        
        try
        {
            if (string.IsNullOrWhiteSpace(json))
                return false;
            
            result = JsonConvert.DeserializeObject<T>(json, DefaultSettings);
            return true;
        }
        catch (Exception ex)
        {
            ETLogManager.Debug($"JSON解析失败: 目标类型={typeof(T).Name}, 错误={ex.Message}");
            return false;
        }
    }
    
    public static bool ValidateJsonSchema(string json, string schema)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(json) || string.IsNullOrWhiteSpace(schema))
                return false;
            
            var jsonSchema = JSchema.Parse(schema);
            var jsonObject = JObject.Parse(json);
            
            var isValid = jsonObject.IsValid(jsonSchema, out IList<string> errorMessages);
            
            if (!isValid)
            {
                ETLogManager.Warning($"JSON架构验证失败: {string.Join(", ", errorMessages)}");
            }
            
            return isValid;
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"JSON架构验证异常: {ex.Message}", ex);
            return false;
        }
    }
    
    public static string MergeJsonObjects(string json1, string json2)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(json1))
                return json2;
            if (string.IsNullOrWhiteSpace(json2))
                return json1;
            
            var obj1 = JObject.Parse(json1);
            var obj2 = JObject.Parse(json2);
            
            // 合并对象，json2的属性会覆盖json1的同名属性
            obj1.Merge(obj2, new JsonMergeSettings
            {
                MergeArrayHandling = MergeArrayHandling.Union,
                MergeNullValueHandling = MergeNullValueHandling.Ignore
            });
            
            var mergedJson = obj1.ToString(Formatting.None);
            ETLogManager.Debug($"JSON对象合并成功: 结果长度={mergedJson.Length}");
            
            return mergedJson;
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"JSON对象合并异常: {ex.Message}", ex);
            throw new ETOAJsonException($"JSON合并失败: {ex.Message}", ex);
        }
    }
    
    public static object ExtractJsonValue(string json, string path)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(json) || string.IsNullOrWhiteSpace(path))
                return null;
            
            var jsonObject = JObject.Parse(json);
            var token = jsonObject.SelectToken(path);
            
            if (token == null)
            {
                ETLogManager.Debug($"JSON路径未找到值: {path}");
                return null;
            }
            
            // 根据token类型返回相应的值
            switch (token.Type)
            {
                case JTokenType.String:
                    return token.Value<string>();
                case JTokenType.Integer:
                    return token.Value<long>();
                case JTokenType.Float:
                    return token.Value<double>();
                case JTokenType.Boolean:
                    return token.Value<bool>();
                case JTokenType.Date:
                    return token.Value<DateTime>();
                case JTokenType.Array:
                case JTokenType.Object:
                    return token.ToString(Formatting.None);
                default:
                    return token.Value<object>();
            }
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"JSON值提取异常: 路径={path}, 错误={ex.Message}", ex);
            return null;
        }
    }
    
    private static JsonSerializerSettings CreateDefaultSettings()
    {
        return new JsonSerializerSettings
        {
            // 忽略null值
            NullValueHandling = NullValueHandling.Ignore,
            
            // 使用驼峰命名
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            
            // 日期格式
            DateFormatString = "yyyy-MM-dd HH:mm:ss",
            DateTimeZoneHandling = DateTimeZoneHandling.Local,
            
            // 错误处理
            Error = HandleSerializationError,
            
            // 类型处理
            TypeNameHandling = TypeNameHandling.None,
            
            // 引用处理
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            
            // 默认值处理
            DefaultValueHandling = DefaultValueHandling.Include,
            
            // 格式化
            Formatting = Formatting.None
        };
    }
    
    private static void HandleSerializationError(object sender, ErrorEventArgs errorArgs)
    {
        ETLogManager.Warning($"JSON序列化警告: {errorArgs.ErrorContext.Error.Message}");
        errorArgs.ErrorContext.Handled = true;
    }
}

public class ETOAJsonException : Exception
{
    public ETOAJsonException(string message) : base(message) { }
    public ETOAJsonException(string message, Exception innerException) : base(message, innerException) { }
}
```

## 🍪 Cookie管理流程

```mermaid
sequenceDiagram
    participant Client as ETOAClient
    participant Helper as ETOACookieHelper
    participant Storage as ETOAStorageHelper
    participant File as 文件系统
    participant Crypto as 加密模块
    
    Note over Client,Crypto: Cookie保存流程
    Client->>Helper: SaveCookies(cookies, filePath)
    Helper->>Helper: 过滤过期Cookie
    Helper->>Helper: 验证Cookie格式
    
    loop 每个Cookie
        Helper->>Helper: 序列化Cookie数据
    end
    
    Helper->>Crypto: 加密Cookie数据
    Crypto-->>Helper: 返回加密结果
    
    Helper->>Storage: 压缩加密数据
    Storage-->>Helper: 返回压缩结果
    
    Helper->>File: 写入Cookie文件
    File-->>Helper: 写入完成
    
    Helper-->>Client: 保存成功
    
    Note over Client,Crypto: Cookie加载流程
    Client->>Helper: LoadCookies(filePath)
    Helper->>File: 检查文件是否存在
    
    alt 文件不存在
        Helper-->>Client: 返回空Cookie列表
    else 文件存在
        Helper->>File: 读取Cookie文件
        File-->>Helper: 返回文件内容
        
        Helper->>Storage: 解压缩数据
        Storage-->>Helper: 返回解压结果
        
        Helper->>Crypto: 解密Cookie数据
        Crypto-->>Helper: 返回解密结果
        
        Helper->>Helper: 反序列化Cookie
        Helper->>Helper: 验证Cookie有效性
        Helper->>Helper: 过滤过期Cookie
        
        Helper-->>Client: 返回有效Cookie列表
    end
```

### Cookie管理实现

```csharp
public class ETOACookieHelper
{
    private static readonly string DefaultCookiePath = Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
        "ETOAAutomation", "Cookies"
    );

    public static void SaveCookies(List<Cookie> cookies, string filePath = null)
    {
        try
        {
            if (cookies == null || cookies.Count == 0)
            {
                ETLogManager.Debug("没有Cookie需要保存");
                return;
            }

            filePath = filePath ?? Path.Combine(DefaultCookiePath, "cookies.dat");
            Directory.CreateDirectory(Path.GetDirectoryName(filePath));

            // 1. 过滤有效Cookie
            var validCookies = FilterExpiredCookies(cookies);

            // 2. 序列化Cookie数据
            var cookieData = validCookies.Select(cookie => new
            {
                Name = cookie.Name,
                Value = cookie.Value,
                Domain = cookie.Domain,
                Path = cookie.Path,
                Expired = cookie.Expired,
                HttpOnly = cookie.HttpOnly,
                Secure = cookie.Secure,
                Expires = cookie.Expires
            }).ToList();

            var json = ETOAJsonHelper.ToJson(cookieData, false);

            // 3. 加密并保存
            ETOAStorageHelper.SaveData(filePath, json, true);

            ETLogManager.Debug($"Cookie已保存: {validCookies.Count} 个, 文件: {filePath}");
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"保存Cookie异常: {ex.Message}", ex);
            throw;
        }
    }

    public static List<Cookie> LoadCookies(string filePath = null)
    {
        try
        {
            filePath = filePath ?? Path.Combine(DefaultCookiePath, "cookies.dat");

            if (!File.Exists(filePath))
            {
                ETLogManager.Debug($"Cookie文件不存在: {filePath}");
                return new List<Cookie>();
            }

            // 1. 加载并解密数据
            var json = ETOAStorageHelper.LoadData<string>(filePath, true);

            if (string.IsNullOrEmpty(json))
            {
                ETLogManager.Warning("Cookie文件为空或解密失败");
                return new List<Cookie>();
            }

            // 2. 反序列化Cookie数据
            var cookieData = ETOAJsonHelper.FromJson<List<dynamic>>(json);

            // 3. 转换为Cookie对象
            var cookies = new List<Cookie>();
            foreach (var data in cookieData)
            {
                try
                {
                    var cookie = new Cookie(data.Name, data.Value, data.Path, data.Domain)
                    {
                        Expired = data.Expired,
                        HttpOnly = data.HttpOnly,
                        Secure = data.Secure
                    };

                    if (DateTime.TryParse(data.Expires?.ToString(), out var expires))
                    {
                        cookie.Expires = expires;
                    }

                    cookies.Add(cookie);
                }
                catch (Exception ex)
                {
                    ETLogManager.Warning($"解析Cookie失败: {ex.Message}");
                }
            }

            // 4. 过滤过期Cookie
            var validCookies = FilterExpiredCookies(cookies);

            ETLogManager.Debug($"Cookie已加载: {validCookies.Count} 个有效, 总共 {cookies.Count} 个");
            return validCookies;
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"加载Cookie异常: {ex.Message}", ex);
            return new List<Cookie>();
        }
    }

    public static List<Cookie> FilterExpiredCookies(List<Cookie> cookies)
    {
        if (cookies == null)
            return new List<Cookie>();

        var now = DateTime.Now;
        return cookies.Where(cookie =>
            !cookie.Expired &&
            (cookie.Expires == DateTime.MinValue || cookie.Expires > now)
        ).ToList();
    }

    public static string GetCookieValue(List<Cookie> cookies, string name)
    {
        return cookies?.FirstOrDefault(c =>
            string.Equals(c.Name, name, StringComparison.OrdinalIgnoreCase))?.Value;
    }

    public static void SetCookieValue(List<Cookie> cookies, string name, string value)
    {
        if (cookies == null) return;

        var existingCookie = cookies.FirstOrDefault(c =>
            string.Equals(c.Name, name, StringComparison.OrdinalIgnoreCase));

        if (existingCookie != null)
        {
            existingCookie.Value = value;
        }
        else
        {
            cookies.Add(new Cookie(name, value));
        }
    }
}
```

## 🔄 重试机制流程

```mermaid
flowchart TD
    A[操作执行请求] --> B[创建重试策略]
    B --> C[执行操作]
    C --> D{操作是否成功}
    D -->|成功| E[返回操作结果]
    D -->|失败| F[检查异常类型]
    F --> G{是否为可重试异常}
    G -->|否| H[返回失败结果]
    G -->|是| I[检查重试次数]
    I --> J{是否超过最大重试次数}
    J -->|是| K[返回最终失败]
    J -->|否| L[计算重试延迟]
    L --> M[等待延迟时间]
    M --> N[记录重试日志]
    N --> O[增加重试计数]
    O --> C

    style A fill:#e1f5fe
    style E fill:#c8e6c9
    style H fill:#ffcdd2
    style K fill:#ffcdd2
```

## 📊 性能监控流程

```mermaid
sequenceDiagram
    participant Client as ETOAClient
    participant Monitor as ETOAPerformanceHelper
    participant Timer as Stopwatch
    participant Metrics as MetricsStore
    participant Log as ETLogManager

    Client->>Monitor: StartTimer("FileUpload")
    Monitor->>Timer: 创建并启动计时器
    Timer-->>Monitor: 返回计时器ID
    Monitor-->>Client: 返回计时器ID

    Note over Client: 执行业务操作

    Client->>Monitor: StopTimer(timerId)
    Monitor->>Timer: 停止计时器
    Timer-->>Monitor: 返回执行时间

    Monitor->>Metrics: 记录性能指标
    Monitor->>Monitor: 检查性能阈值

    alt 超过阈值
        Monitor->>Log: 记录性能警告
    else 正常范围
        Monitor->>Log: 记录性能信息
    end

    Monitor-->>Client: 返回执行时间

    Note over Client,Log: 性能报告生成
    Client->>Monitor: GetMetricSummary("FileUpload")
    Monitor->>Metrics: 查询历史数据
    Metrics-->>Monitor: 返回统计数据
    Monitor->>Monitor: 计算平均值、最大值等
    Monitor-->>Client: 返回性能摘要
```

---

**📅 文档版本**: v1.0
**🔄 最后更新**: 2024年12月
**👨‍💻 维护团队**: ETOAAutomation开发组
