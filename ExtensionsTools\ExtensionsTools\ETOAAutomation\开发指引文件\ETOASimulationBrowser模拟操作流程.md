# 🖱️ ETOASimulationBrowser 模拟操作流程详解

## 📋 概述

ETOASimulationBrowser是基于CefSharp的网页自动化操作模块，支持DOM操作和坐标操作两种模式，提供页面导航、元素操作、事件模拟、截图录制等功能。

## 🏗️ 类结构图

```mermaid
classDiagram
    class ETOASimulationBrowser {
        +ChromiumWebBrowser Browser
        +OperationMode CurrentMode
        +bool IsPageLoaded
        +string CurrentUrl
        +Dictionary~string,object~ PageElements
        
        +NavigateAsync(url) Task~bool~
        +WaitForPageLoadAsync(timeout) Task~bool~
        +FillTextAsync(selector, text) Task~bool~
        +ClickElementAsync(selector) Task~bool~
        +SelectOptionAsync(selector, value) Task~bool~
        +SubmitFormAsync(selector) Task~bool~
        +ClickAtAsync(x, y) Task~bool~
        +SendKeysAsync(text) Task~bool~
        +TakeScreenshotAsync() Task~byte[]~
        
        -ExecuteJavaScript(script) Task~object~
        -FindElement(selector) Task~bool~
        -WaitForElement(selector, timeout) Task~bool~
        -SimulateMouseClick(x, y) void
        -SimulateKeyboardInput(text) void
        -ConvertToScreenCoordinates(x, y) Point
    }
    
    ETOASimulationBrowser --> ChromiumWebBrowser
    ETOASimulationBrowser --> OperationMode
    ETOASimulationBrowser --> ETOABrowserAutomationHelper
    ETOASimulationBrowser --> ETOAConfigHelper
```

## 🔄 操作模式选择流程

```mermaid
flowchart TD
    A[开始自动化操作] --> B[分析操作类型]
    B --> C{操作类型判断}
    C -->|元素操作| D[选择DOM操作模式]
    C -->|坐标操作| E[选择坐标操作模式]
    C -->|混合操作| F[智能模式选择]
    
    D --> G[检查页面DOM结构]
    G --> H{DOM是否可访问}
    H -->|是| I[使用JavaScript操作]
    H -->|否| J[降级到坐标模式]
    
    E --> K[计算屏幕坐标]
    K --> L[使用Windows API操作]
    
    F --> M[尝试DOM操作]
    M --> N{DOM操作是否成功}
    N -->|是| O[继续DOM模式]
    N -->|否| P[切换到坐标模式]
    
    I --> Q[执行DOM操作]
    J --> R[执行坐标操作]
    L --> R
    O --> Q
    P --> R
    
    Q --> S{操作是否成功}
    R --> S
    S -->|是| T[返回操作成功]
    S -->|否| U[记录操作失败]
    U --> V[返回操作失败]
    
    style A fill:#e1f5fe
    style T fill:#c8e6c9
    style V fill:#ffcdd2
```

## 🌐 页面导航和加载流程

```mermaid
sequenceDiagram
    participant Client as ETOAClient
    participant Browser as ETOASimulationBrowser
    participant Cef as ChromiumWebBrowser
    participant Page as 目标页面
    participant Monitor as LoadMonitor
    
    Client->>Browser: NavigateAsync(url)
    Browser->>Browser: 验证URL格式
    
    alt URL无效
        Browser-->>Client: 返回URL无效错误
    else URL有效
        Browser->>Cef: Navigate(url)
        Browser->>Monitor: 启动加载监控
        
        Cef->>Page: 发送HTTP请求
        Page-->>Cef: 返回页面内容
        
        Cef->>Browser: OnLoadingStateChanged事件
        Browser->>Monitor: 更新加载状态
        
        Cef->>Browser: OnFrameLoadEnd事件
        Browser->>Browser: 检查页面DOM完整性
        
        Browser->>Browser: WaitForPageLoadAsync()
        
        loop 等待页面完全加载
            Browser->>Browser: 检查关键元素
            Browser->>Browser: 检查JavaScript执行状态
            Browser->>Browser: 检查网络请求状态
            
            alt 页面加载完成
                Browser->>Monitor: 停止加载监控
                Browser-->>Client: 返回导航成功
            else 加载超时
                Browser->>Monitor: 停止加载监控
                Browser-->>Client: 返回加载超时错误
            end
        end
    end
```

### 页面导航实现

```csharp
public async Task<bool> NavigateAsync(string url, int timeoutSeconds = 30)
{
    try
    {
        ETLogManager.Info($"开始导航到页面: {url}");
        
        // 1. 验证URL格式
        if (!Uri.TryCreate(url, UriKind.Absolute, out var uri))
        {
            ETLogManager.Error($"无效的URL格式: {url}");
            return false;
        }
        
        // 2. 设置加载状态
        IsPageLoaded = false;
        CurrentUrl = url;
        
        // 3. 创建加载完成任务
        var loadCompletionSource = new TaskCompletionSource<bool>();
        var timeoutCancellation = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));
        
        // 4. 注册加载事件
        EventHandler<LoadingStateChangedEventArgs> loadingHandler = null;
        EventHandler<FrameLoadEndEventArgs> frameLoadHandler = null;
        
        loadingHandler = (sender, args) =>
        {
            if (!args.IsLoading)
            {
                // 页面基础加载完成，等待DOM就绪
                _ = Task.Run(async () =>
                {
                    await WaitForDomReady();
                    loadCompletionSource.TrySetResult(true);
                });
            }
        };
        
        frameLoadHandler = (sender, args) =>
        {
            if (args.Frame.IsMain)
            {
                ETLogManager.Debug($"主框架加载完成: {args.Url}");
            }
        };
        
        Browser.LoadingStateChanged += loadingHandler;
        Browser.FrameLoadEnd += frameLoadHandler;
        
        try
        {
            // 5. 开始导航
            Browser.Load(url);
            
            // 6. 等待加载完成或超时
            var loadTask = loadCompletionSource.Task;
            var timeoutTask = Task.Delay(timeoutSeconds * 1000, timeoutCancellation.Token);
            
            var completedTask = await Task.WhenAny(loadTask, timeoutTask);
            
            if (completedTask == loadTask && loadTask.Result)
            {
                IsPageLoaded = true;
                ETLogManager.Info($"页面导航成功: {url}");
                return true;
            }
            else
            {
                ETLogManager.Error($"页面导航超时: {url}");
                return false;
            }
        }
        finally
        {
            // 7. 清理事件处理器
            Browser.LoadingStateChanged -= loadingHandler;
            Browser.FrameLoadEnd -= frameLoadHandler;
            timeoutCancellation.Cancel();
        }
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"页面导航异常: {url}, 错误: {ex.Message}", ex);
        return false;
    }
}

private async Task WaitForDomReady()
{
    try
    {
        var script = @"
            (function() {
                return document.readyState === 'complete' && 
                       document.body !== null &&
                       window.jQuery === undefined || jQuery.active === 0;
            })();
        ";
        
        for (int i = 0; i < 30; i++) // 最多等待30秒
        {
            var result = await Browser.EvaluateScriptAsync(script);
            if (result.Success && result.Result is bool isReady && isReady)
            {
                ETLogManager.Debug("DOM已就绪");
                return;
            }
            
            await Task.Delay(1000); // 等待1秒后重试
        }
        
        ETLogManager.Warning("DOM就绪检查超时");
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"DOM就绪检查异常: {ex.Message}", ex);
    }
}
```

## 🎯 DOM操作模式流程

```mermaid
flowchart TD
    A[DOM操作请求] --> B[检查页面DOM状态]
    B --> C{DOM是否可访问}
    C -->|否| D[返回DOM不可访问错误]
    C -->|是| E[构建JavaScript脚本]
    E --> F[注入脚本到页面]
    F --> G[执行元素查找]
    G --> H{元素是否存在}
    H -->|否| I[等待元素出现]
    H -->|是| J[检查元素可见性]
    J --> K{元素是否可见}
    K -->|否| L[滚动到元素位置]
    K -->|是| M[检查元素可操作性]
    M --> N{元素是否可操作}
    N -->|否| O[等待元素可操作]
    N -->|是| P[执行具体操作]
    P --> Q{操作是否成功}
    Q -->|是| R[触发相关事件]
    Q -->|否| S[记录操作失败]
    R --> T[返回操作成功]
    S --> U[返回操作失败]
    
    I --> V{等待是否超时}
    V -->|否| G
    V -->|是| W[返回元素未找到错误]
    
    L --> X[滚动完成]
    X --> M
    
    O --> Y{等待是否超时}
    Y -->|否| M
    Y -->|是| Z[返回元素不可操作错误]
    
    style A fill:#e1f5fe
    style T fill:#c8e6c9
    style D fill:#ffcdd2
    style U fill:#ffcdd2
    style W fill:#ffcdd2
    style Z fill:#ffcdd2
```

### DOM操作实现

```csharp
public async Task<bool> FillTextAsync(string selector, string text, int timeoutSeconds = 10)
{
    try
    {
        ETLogManager.Debug($"开始填充文本: 选择器={selector}, 文本={text}");
        
        // 1. 等待元素出现
        if (!await WaitForElementAsync(selector, timeoutSeconds))
        {
            ETLogManager.Error($"元素未找到: {selector}");
            return false;
        }
        
        // 2. 构建填充脚本
        var script = $@"
            (function() {{
                try {{
                    // 查找元素
                    var element = document.querySelector('{selector.Replace("'", "\\'")}');
                    if (!element) {{
                        return {{ success: false, error: '元素未找到' }};
                    }}
                    
                    // 检查元素类型
                    if (element.tagName.toLowerCase() !== 'input' && 
                        element.tagName.toLowerCase() !== 'textarea') {{
                        return {{ success: false, error: '元素不是输入框' }};
                    }}
                    
                    // 检查元素是否可见
                    var rect = element.getBoundingClientRect();
                    if (rect.width === 0 || rect.height === 0) {{
                        return {{ success: false, error: '元素不可见' }};
                    }}
                    
                    // 检查元素是否可编辑
                    if (element.disabled || element.readOnly) {{
                        return {{ success: false, error: '元素不可编辑' }};
                    }}
                    
                    // 滚动到元素位置
                    element.scrollIntoView({{ behavior: 'smooth', block: 'center' }});
                    
                    // 聚焦元素
                    element.focus();
                    
                    // 清空现有内容
                    element.value = '';
                    
                    // 填充新内容
                    element.value = '{text.Replace("'", "\\'")}';
                    
                    // 触发相关事件
                    element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    element.dispatchEvent(new Event('blur', {{ bubbles: true }}));
                    
                    return {{ success: true, value: element.value }};
                }} catch (error) {{
                    return {{ success: false, error: error.message }};
                }}
            }})();
        ";
        
        // 3. 执行脚本
        var result = await Browser.EvaluateScriptAsync(script);
        
        if (result.Success && result.Result != null)
        {
            var resultObj = JsonConvert.DeserializeObject<dynamic>(result.Result.ToString());
            
            if (resultObj.success == true)
            {
                ETLogManager.Debug($"文本填充成功: {selector}");
                return true;
            }
            else
            {
                ETLogManager.Error($"文本填充失败: {selector}, 错误: {resultObj.error}");
                return false;
            }
        }
        else
        {
            ETLogManager.Error($"脚本执行失败: {selector}, 错误: {result.Message}");
            return false;
        }
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"填充文本异常: {selector}, 错误: {ex.Message}", ex);
        return false;
    }
}

public async Task<bool> ClickElementAsync(string selector, int timeoutSeconds = 10)
{
    try
    {
        ETLogManager.Debug($"开始点击元素: {selector}");
        
        // 1. 等待元素出现
        if (!await WaitForElementAsync(selector, timeoutSeconds))
        {
            ETLogManager.Error($"元素未找到: {selector}");
            return false;
        }
        
        // 2. 构建点击脚本
        var script = $@"
            (function() {{
                try {{
                    var element = document.querySelector('{selector.Replace("'", "\\'")}');
                    if (!element) {{
                        return {{ success: false, error: '元素未找到' }};
                    }}
                    
                    // 检查元素是否可见
                    var rect = element.getBoundingClientRect();
                    if (rect.width === 0 || rect.height === 0) {{
                        return {{ success: false, error: '元素不可见' }};
                    }}
                    
                    // 检查元素是否可点击
                    if (element.disabled) {{
                        return {{ success: false, error: '元素已禁用' }};
                    }}
                    
                    // 滚动到元素位置
                    element.scrollIntoView({{ behavior: 'smooth', block: 'center' }});
                    
                    // 模拟点击事件
                    element.dispatchEvent(new MouseEvent('mousedown', {{ bubbles: true }}));
                    element.dispatchEvent(new MouseEvent('mouseup', {{ bubbles: true }}));
                    element.dispatchEvent(new MouseEvent('click', {{ bubbles: true }}));
                    
                    // 如果是链接或按钮，触发相应事件
                    if (element.tagName.toLowerCase() === 'a' || 
                        element.tagName.toLowerCase() === 'button' ||
                        element.type === 'submit') {{
                        element.click();
                    }}
                    
                    return {{ success: true, tagName: element.tagName }};
                }} catch (error) {{
                    return {{ success: false, error: error.message }};
                }}
            }})();
        ";
        
        // 3. 执行脚本
        var result = await Browser.EvaluateScriptAsync(script);
        
        if (result.Success && result.Result != null)
        {
            var resultObj = JsonConvert.DeserializeObject<dynamic>(result.Result.ToString());
            
            if (resultObj.success == true)
            {
                ETLogManager.Debug($"元素点击成功: {selector}");
                return true;
            }
            else
            {
                ETLogManager.Error($"元素点击失败: {selector}, 错误: {resultObj.error}");
                return false;
            }
        }
        else
        {
            ETLogManager.Error($"脚本执行失败: {selector}, 错误: {result.Message}");
            return false;
        }
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"点击元素异常: {selector}, 错误: {ex.Message}", ex);
        return false;
    }
}
```

## 🖱️ 坐标操作模式流程

```mermaid
sequenceDiagram
    participant Browser as ETOASimulationBrowser
    participant Coord as CoordinateCalculator
    participant WinAPI as Windows API
    participant Screen as 屏幕
    
    Browser->>Browser: ClickAtAsync(x, y)
    Browser->>Coord: 转换浏览器坐标到屏幕坐标
    Coord->>Coord: 获取浏览器窗口位置
    Coord->>Coord: 计算相对坐标
    Coord-->>Browser: 返回屏幕坐标
    
    Browser->>Browser: 验证坐标有效性
    
    alt 坐标无效
        Browser-->>Browser: 返回坐标无效错误
    else 坐标有效
        Browser->>WinAPI: SetCursorPos(screenX, screenY)
        WinAPI->>Screen: 移动鼠标到目标位置
        Screen-->>WinAPI: 鼠标位置已更新
        
        Browser->>WinAPI: SendInput(MOUSEEVENTF_LEFTDOWN)
        WinAPI->>Screen: 发送鼠标按下事件
        Screen-->>WinAPI: 事件已发送
        
        Browser->>Browser: 等待短暂延迟
        
        Browser->>WinAPI: SendInput(MOUSEEVENTF_LEFTUP)
        WinAPI->>Screen: 发送鼠标释放事件
        Screen-->>WinAPI: 事件已发送
        
        Browser-->>Browser: 返回点击成功
    end
```

### 坐标操作实现

```csharp
public async Task<bool> ClickAtAsync(int x, int y, int delayMs = 100)
{
    try
    {
        ETLogManager.Debug($"开始坐标点击: ({x}, {y})");
        
        // 1. 转换为屏幕坐标
        var screenPoint = ConvertToScreenCoordinates(x, y);
        
        if (screenPoint.X < 0 || screenPoint.Y < 0)
        {
            ETLogManager.Error($"无效的屏幕坐标: ({screenPoint.X}, {screenPoint.Y})");
            return false;
        }
        
        // 2. 移动鼠标到目标位置
        if (!SetCursorPos(screenPoint.X, screenPoint.Y))
        {
            ETLogManager.Error($"移动鼠标失败: ({screenPoint.X}, {screenPoint.Y})");
            return false;
        }
        
        // 3. 等待鼠标移动完成
        await Task.Delay(50);
        
        // 4. 发送鼠标点击事件
        var inputs = new INPUT[2];
        
        // 鼠标按下
        inputs[0] = new INPUT
        {
            type = INPUT_MOUSE,
            u = new InputUnion
            {
                mi = new MOUSEINPUT
                {
                    dx = 0,
                    dy = 0,
                    mouseData = 0,
                    dwFlags = MOUSEEVENTF_LEFTDOWN,
                    time = 0,
                    dwExtraInfo = IntPtr.Zero
                }
            }
        };
        
        // 鼠标释放
        inputs[1] = new INPUT
        {
            type = INPUT_MOUSE,
            u = new InputUnion
            {
                mi = new MOUSEINPUT
                {
                    dx = 0,
                    dy = 0,
                    mouseData = 0,
                    dwFlags = MOUSEEVENTF_LEFTUP,
                    time = 0,
                    dwExtraInfo = IntPtr.Zero
                }
            }
        };
        
        // 发送输入事件
        var result = SendInput((uint)inputs.Length, inputs, Marshal.SizeOf(typeof(INPUT)));
        
        if (result != inputs.Length)
        {
            ETLogManager.Error($"发送鼠标事件失败: 期望 {inputs.Length}, 实际 {result}");
            return false;
        }
        
        // 5. 等待点击完成
        await Task.Delay(delayMs);
        
        ETLogManager.Debug($"坐标点击成功: ({x}, {y}) -> ({screenPoint.X}, {screenPoint.Y})");
        return true;
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"坐标点击异常: ({x}, {y}), 错误: {ex.Message}", ex);
        return false;
    }
}

public async Task<bool> SendKeysAsync(string text, int delayBetweenKeys = 50)
{
    try
    {
        ETLogManager.Debug($"开始发送键盘输入: {text}");
        
        foreach (char c in text)
        {
            // 转换字符为虚拟键码
            var vkCode = VkKeyScan(c);
            var scanCode = MapVirtualKey((uint)(vkCode & 0xFF), 0);
            
            var inputs = new INPUT[2];
            
            // 按键按下
            inputs[0] = new INPUT
            {
                type = INPUT_KEYBOARD,
                u = new InputUnion
                {
                    ki = new KEYBDINPUT
                    {
                        wVk = (ushort)(vkCode & 0xFF),
                        wScan = (ushort)scanCode,
                        dwFlags = 0,
                        time = 0,
                        dwExtraInfo = IntPtr.Zero
                    }
                }
            };
            
            // 按键释放
            inputs[1] = new INPUT
            {
                type = INPUT_KEYBOARD,
                u = new InputUnion
                {
                    ki = new KEYBDINPUT
                    {
                        wVk = (ushort)(vkCode & 0xFF),
                        wScan = (ushort)scanCode,
                        dwFlags = KEYEVENTF_KEYUP,
                        time = 0,
                        dwExtraInfo = IntPtr.Zero
                    }
                }
            };
            
            // 发送按键事件
            var result = SendInput((uint)inputs.Length, inputs, Marshal.SizeOf(typeof(INPUT)));
            
            if (result != inputs.Length)
            {
                ETLogManager.Warning($"发送按键事件失败: 字符 '{c}', 期望 {inputs.Length}, 实际 {result}");
            }
            
            // 按键间延迟
            if (delayBetweenKeys > 0)
            {
                await Task.Delay(delayBetweenKeys);
            }
        }
        
        ETLogManager.Debug($"键盘输入发送完成: {text}");
        return true;
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"发送键盘输入异常: {text}, 错误: {ex.Message}", ex);
        return false;
    }
}

private Point ConvertToScreenCoordinates(int browserX, int browserY)
{
    try
    {
        // 获取浏览器控件在屏幕上的位置
        var browserLocation = Browser.PointToScreen(new Point(0, 0));
        
        // 计算屏幕坐标
        var screenX = browserLocation.X + browserX;
        var screenY = browserLocation.Y + browserY;
        
        return new Point(screenX, screenY);
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"坐标转换异常: ({browserX}, {browserY}), 错误: {ex.Message}", ex);
        return new Point(-1, -1);
    }
}
```

## 📸 截图和录制功能

```mermaid
graph TD
    A[截图请求] --> B[检查浏览器状态]
    B --> C{浏览器是否就绪}
    C -->|否| D[返回浏览器未就绪错误]
    C -->|是| E[获取页面尺寸]
    E --> F[创建截图参数]
    F --> G[执行截图操作]
    G --> H{截图是否成功}
    H -->|否| I[返回截图失败错误]
    H -->|是| J[处理截图数据]
    J --> K[压缩图片质量]
    K --> L[添加水印信息]
    L --> M[保存截图文件]
    M --> N[返回截图结果]
    
    style A fill:#e1f5fe
    style N fill:#c8e6c9
    style D fill:#ffcdd2
    style I fill:#ffcdd2
```

---

**📅 文档版本**: v1.0  
**🔄 最后更新**: 2024年12月  
**👨‍💻 维护团队**: ETOAAutomation开发组
