using System;
using System.Collections.Generic;
using System.IO;

using ET;

namespace ET.ETOAAutomation.Helpers
{
    /// <summary>
    /// 文件上传配置管理辅助类
    /// </summary>
    public static class ETOAUploadConfigHelper
    {
        #region 私有字段

        private static readonly string _configPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "ETOAAutomation", "UploadConfig.ini");

        private const string UPLOAD_SECTION = "Upload";
        private const string ADVANCED_SECTION = "Advanced";
        private const string RESTRICTIONS_SECTION = "Restrictions";

        /// <summary>
        /// 获取INI文件实例
        /// </summary>
        private static ETIniFile GetIniFile()
        {
            return new ETIniFile(_configPath);
        }

        #endregion 私有字段

        #region 初始化方法

        /// <summary>
        /// 初始化上传配置
        /// </summary>
        public static void Initialize()
        {
            try
            {
                var configDir = Path.GetDirectoryName(_configPath);
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                if (!File.Exists(_configPath))
                {
                    CreateDefaultConfig();
                }

                ETLogManager.Info("ETOAUploadConfigHelper", "上传配置初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadConfigHelper", $"初始化配置失败: {ex.Message}");
                throw new ETException("初始化上传配置失败", ex);
            }
        }

        /// <summary>
        /// 创建默认配置（简化版本）
        /// </summary>
        private static void CreateDefaultConfig()
        {
            // 基础上传配置
            SetMaxFileSize(100);
            SetRetryCount(3);
            SetRequestTimeout(300); // 5分钟

            // 文件限制配置
            SetAllowedExtensions(new string[]
            {
                ".jpg", ".jpeg", ".png", ".gif", ".bmp",
                ".pdf", ".doc", ".docx", ".xls", ".xlsx",
                ".ppt", ".pptx", ".txt", ".zip", ".rar"
            });

            ETLogManager.Info("ETOAUploadConfigHelper", "创建默认上传配置（简化版本）");
        }

        #endregion 初始化方法

        #region 基础配置方法

        /// <summary>
        /// 获取最大文件大小（MB）
        /// </summary>
        public static int GetMaxFileSize()
        {
            return GetIniFile().ReadInt(UPLOAD_SECTION, "MaxFileSize", 100);
        }

        /// <summary>
        /// 设置最大文件大小（MB）
        /// </summary>
        public static void SetMaxFileSize(int maxSizeMB)
        {
            GetIniFile().WriteString(UPLOAD_SECTION, "MaxFileSize", maxSizeMB.ToString());
        }



        /// <summary>
        /// 获取重试次数
        /// </summary>
        public static int GetRetryCount()
        {
            return GetIniFile().ReadInt(UPLOAD_SECTION, "RetryCount", 3);
        }

        /// <summary>
        /// 设置重试次数
        /// </summary>
        public static void SetRetryCount(int retryCount)
        {
            GetIniFile().WriteString(UPLOAD_SECTION, "RetryCount", retryCount.ToString());
        }

        /// <summary>
        /// 获取请求超时时间（秒）
        /// </summary>
        public static int GetRequestTimeout()
        {
            return GetIniFile().ReadInt(UPLOAD_SECTION, "RequestTimeout", 300);
        }

        /// <summary>
        /// 设置请求超时时间（秒）
        /// </summary>
        public static void SetRequestTimeout(int timeoutSeconds)
        {
            GetIniFile().WriteString(UPLOAD_SECTION, "RequestTimeout", timeoutSeconds.ToString());
        }

        #endregion 基础配置方法



        #region 文件限制配置

        /// <summary>
        /// 获取允许的文件扩展名
        /// </summary>
        public static string[] GetAllowedExtensions()
        {
            var extensionsStr = GetIniFile().ReadString(RESTRICTIONS_SECTION, "AllowedExtensions",
                ".jpg,.jpeg,.png,.gif,.bmp,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar");

            return extensionsStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
        }

        /// <summary>
        /// 设置允许的文件扩展名
        /// </summary>
        public static void SetAllowedExtensions(string[] extensions)
        {
            var extensionsStr = string.Join(",", extensions);
            GetIniFile().WriteString(RESTRICTIONS_SECTION, "AllowedExtensions", extensionsStr);
        }

        /// <summary>
        /// 添加允许的文件扩展名
        /// </summary>
        public static void AddAllowedExtension(string extension)
        {
            var currentExtensions = new List<string>(GetAllowedExtensions());
            if (!currentExtensions.Contains(extension.ToLower()))
            {
                currentExtensions.Add(extension.ToLower());
                SetAllowedExtensions(currentExtensions.ToArray());
            }
        }

        /// <summary>
        /// 移除允许的文件扩展名
        /// </summary>
        public static void RemoveAllowedExtension(string extension)
        {
            var currentExtensions = new List<string>(GetAllowedExtensions());
            if (currentExtensions.Contains(extension.ToLower()))
            {
                currentExtensions.Remove(extension.ToLower());
                SetAllowedExtensions(currentExtensions.ToArray());
            }
        }

        /// <summary>
        /// 检查文件扩展名是否被允许
        /// </summary>
        public static bool IsExtensionAllowed(string extension)
        {
            var allowedExtensions = GetAllowedExtensions();
            return Array.Exists(allowedExtensions, ext =>
                string.Equals(ext, extension, StringComparison.OrdinalIgnoreCase));
        }

        #endregion 文件限制配置

        #region 配置应用方法

        /// <summary>
        /// 应用配置到文件上传器（简化版本）
        /// </summary>
        public static void ApplyConfigToUploader(ETOAFileUploader uploader)
        {
            try
            {
                if (uploader == null)
                    throw new ArgumentNullException(nameof(uploader));

                uploader.MaxFileSize = GetMaxFileSize();
                uploader.RetryCount = GetRetryCount();
                uploader.AllowedExtensions = GetAllowedExtensions();

                ETLogManager.Info("ETOAUploadConfigHelper", "配置已应用到文件上传器（简化版本）");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadConfigHelper", $"应用配置失败: {ex.Message}");
                throw new ETException("应用上传配置失败", ex);
            }
        }

        /// <summary>
        /// 从文件上传器获取配置（简化版本）
        /// </summary>
        public static void GetConfigFromUploader(ETOAFileUploader uploader)
        {
            try
            {
                if (uploader == null)
                    throw new ArgumentNullException(nameof(uploader));

                SetMaxFileSize(uploader.MaxFileSize);
                SetRetryCount(uploader.RetryCount);
                SetAllowedExtensions(uploader.AllowedExtensions);

                ETLogManager.Info("ETOAUploadConfigHelper", "从文件上传器获取配置（简化版本）");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadConfigHelper", $"获取配置失败: {ex.Message}");
                throw new ETException("获取上传配置失败", ex);
            }
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public static void ResetToDefault()
        {
            try
            {
                if (File.Exists(_configPath))
                {
                    File.Delete(_configPath);
                }
                CreateDefaultConfig();
                ETLogManager.Info("ETOAUploadConfigHelper", "重置为默认配置");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadConfigHelper", $"重置配置失败: {ex.Message}");
                throw new ETException("重置上传配置失败", ex);
            }
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        public static string GetConfigFilePath()
        {
            return _configPath;
        }

        /// <summary>
        /// 检查配置文件是否存在
        /// </summary>
        public static bool ConfigFileExists()
        {
            return File.Exists(_configPath);
        }

        #endregion 配置应用方法
    }
}