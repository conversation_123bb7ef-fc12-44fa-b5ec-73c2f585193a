using System;
using System.Linq;
using System.Windows.Forms;
using ET.ETOAAutomation.Helpers;
using ET;

namespace ET.ETOAAutomation
{
    /// <summary>
    /// 文件上传配置窗体
    /// </summary>
    public partial class ETOAUploadConfigForm : Form
    {
        #region 私有字段

        private bool _isLoading = false;

        #endregion 私有字段

        #region 构造函数

        /// <summary>
        /// 初始化文件上传配置窗体
        /// </summary>
        public ETOAUploadConfigForm()
        {
            InitializeComponent();
            LoadConfiguration();
            SetupEventHandlers();

            ETLogManager.Info("ETOAUploadConfigForm", "文件上传配置窗体初始化完成");
        }

        #endregion 构造函数

        #region 初始化方法

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            this.Load += ETOAUploadConfigForm_Load;
            BtnSave.Click += BtnSave_Click;
            BtnCancel.Click += BtnCancel_Click;
            BtnReset.Click += BtnReset_Click;
            BtnAddExtension.Click += BtnAddExtension_Click;
            BtnRemoveExtension.Click += BtnRemoveExtension_Click;
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                _isLoading = true;

                // 初始化配置
                ETOAUploadConfigHelper.Initialize();

                // 加载基础配置（简化版本）
                NumMaxFileSize.Value = ETOAUploadConfigHelper.GetMaxFileSize();
                NumRetryCount.Value = ETOAUploadConfigHelper.GetRetryCount();
                NumTimeout.Value = ETOAUploadConfigHelper.GetRequestTimeout();

                // 禁用已移除的复杂功能控件
                NumChunkSize.Enabled = false;
                NumMaxConcurrent.Enabled = false;
                ChkResumableUpload.Enabled = false;
                ChkIntegrityCheck.Enabled = false;
                ChkAutoRetry.Enabled = false;
                NumProgressInterval.Enabled = false;

                // 设置默认值并添加说明
                NumChunkSize.Value = 1; // 不再使用
                NumMaxConcurrent.Value = 1; // 不再使用
                ChkResumableUpload.Checked = false; // 已移除
                ChkIntegrityCheck.Checked = false; // 已移除
                ChkAutoRetry.Checked = false; // 已移除
                NumProgressInterval.Value = 1000; // 不再使用

                // 加载文件扩展名
                LoadAllowedExtensions();

                _isLoading = false;
                ETLogManager.Info("ETOAUploadConfigForm", "配置加载完成");
            }
            catch (Exception ex)
            {
                _isLoading = false;
                ETLogManager.Error("ETOAUploadConfigForm", $"加载配置失败: {ex.Message}");
                MessageBox.Show($"加载配置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载允许的文件扩展名
        /// </summary>
        private void LoadAllowedExtensions()
        {
            try
            {
                var extensions = ETOAUploadConfigHelper.GetAllowedExtensions();
                LstExtensions.Items.Clear();

                foreach (var extension in extensions)
                {
                    LstExtensions.Items.Add(extension);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadConfigForm", $"加载文件扩展名失败: {ex.Message}");
            }
        }

        #endregion 初始化方法

        #region 事件处理

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void ETOAUploadConfigForm_Load(object sender, EventArgs e)
        {
            // 设置窗体标题
            this.Text = "文件上传配置 (简化版本)";

            // 设置默认按钮
            this.AcceptButton = BtnSave;
            this.CancelButton = BtnCancel;

            // 添加简化版本说明
            UpdateControlLabels();
        }

        /// <summary>
        /// 更新控件标签，添加简化版本说明
        /// </summary>
        private void UpdateControlLabels()
        {
            // 为已禁用的控件添加说明
            if (NumChunkSize.Parent is GroupBox groupBox)
            {
                foreach (Control control in groupBox.Controls)
                {
                    if (control is Label label)
                    {
                        if (label.Text.Contains("分块大小") || label.Text.Contains("ChunkSize"))
                        {
                            label.Text += " (已简化)";
                        }
                        else if (label.Text.Contains("并发数") || label.Text.Contains("Concurrent"))
                        {
                            label.Text += " (已简化)";
                        }
                    }
                }
            }

            // 为高级功能添加说明
            ChkResumableUpload.Text += " (已移除)";
            ChkIntegrityCheck.Text += " (已移除)";
            ChkAutoRetry.Text += " (已移除)";
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateConfiguration())
                {
                    return;
                }

                SaveConfiguration();

                MessageBox.Show("配置保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadConfigForm", $"保存配置失败: {ex.Message}");
                MessageBox.Show($"保存配置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("确定要重置为默认配置吗？", "确认重置",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    ETOAUploadConfigHelper.ResetToDefault();
                    LoadConfiguration();
                    MessageBox.Show("配置已重置为默认值！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    ETLogManager.Error("ETOAUploadConfigForm", $"重置配置失败: {ex.Message}");
                    MessageBox.Show($"重置配置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 添加扩展名按钮点击事件
        /// </summary>
        private void BtnAddExtension_Click(object sender, EventArgs e)
        {
            var extension = TxtNewExtension.Text.Trim();

            if (string.IsNullOrEmpty(extension))
            {
                MessageBox.Show("请输入文件扩展名！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!extension.StartsWith("."))
            {
                extension = "." + extension;
            }

            if (LstExtensions.Items.Contains(extension))
            {
                MessageBox.Show("该扩展名已存在！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            LstExtensions.Items.Add(extension);
            TxtNewExtension.Clear();
            TxtNewExtension.Focus();
        }

        /// <summary>
        /// 移除扩展名按钮点击事件
        /// </summary>
        private void BtnRemoveExtension_Click(object sender, EventArgs e)
        {
            if (LstExtensions.SelectedItem != null)
            {
                LstExtensions.Items.Remove(LstExtensions.SelectedItem);
            }
            else
            {
                MessageBox.Show("请选择要移除的扩展名！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        #endregion 事件处理

        #region 私有方法

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <returns>是否验证通过</returns>
        private bool ValidateConfiguration()
        {
            // 验证最大文件大小
            if (NumMaxFileSize.Value <= 0)
            {
                MessageBox.Show("最大文件大小必须大于0！", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                NumMaxFileSize.Focus();
                return false;
            }

            // 验证分块大小
            if (NumChunkSize.Value <= 0)
            {
                MessageBox.Show("分块大小必须大于0！", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                NumChunkSize.Focus();
                return false;
            }

            // 验证最大并发数
            if (NumMaxConcurrent.Value <= 0)
            {
                MessageBox.Show("最大并发数必须大于0！", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                NumMaxConcurrent.Focus();
                return false;
            }

            // 验证超时时间
            if (NumTimeout.Value <= 0)
            {
                MessageBox.Show("超时时间必须大于0！", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                NumTimeout.Focus();
                return false;
            }

            // 验证进度更新间隔
            if (NumProgressInterval.Value <= 0)
            {
                MessageBox.Show("进度更新间隔必须大于0！", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                NumProgressInterval.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// 保存配置（简化版本）
        /// </summary>
        private void SaveConfiguration()
        {
            // 保存基础配置（仅保留简化后支持的配置项）
            ETOAUploadConfigHelper.SetMaxFileSize((int)NumMaxFileSize.Value);
            ETOAUploadConfigHelper.SetRetryCount((int)NumRetryCount.Value);
            ETOAUploadConfigHelper.SetRequestTimeout((int)NumTimeout.Value);

            // 保存文件扩展名
            var extensions = LstExtensions.Items.Cast<string>().ToArray();
            ETOAUploadConfigHelper.SetAllowedExtensions(extensions);

            ETLogManager.Info("ETOAUploadConfigForm", "简化版配置保存完成");
        }

        #endregion 私有方法

        #region 公共方法

        /// <summary>
        /// 显示配置对话框
        /// </summary>
        /// <param name="parent">父窗体</param>
        /// <returns>对话框结果</returns>
        public static DialogResult ShowConfigDialog(Form parent = null)
        {
            try
            {
                using (var configForm = new ETOAUploadConfigForm())
                {
                    return configForm.ShowDialog(parent);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadConfigForm", $"显示配置对话框失败: {ex.Message}");
                MessageBox.Show($"显示配置对话框失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        #endregion 公共方法
    }
}