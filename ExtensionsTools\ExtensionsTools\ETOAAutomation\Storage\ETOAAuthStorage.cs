using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ET.ETOAAutomation.Models;

using ET;

namespace ET.ETOAAutomation.Storage
{
    /// <summary>
    /// 认证信息存储类，负责安全存储和管理用户认证信息
    /// </summary>
    public class ETOAAuthStorage
    {
        #region 私有字段

        private readonly string _configPath;
        private const string AUTH_SECTION = "Authentication";

        #endregion 私有字段

        #region 构造函数

        /// <summary>
        /// 初始化认证信息存储
        /// </summary>
        public ETOAAuthStorage()
        {
            try
            {
                // 创建配置文件路径
                string configDir = Path.Combine(
                    Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location),
                    "Config");

                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                _configPath = Path.Combine(configDir, "ETOAAuth.ini");

                ETLogManager.Info($"认证存储初始化完成，配置文件: {_configPath}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("认证存储初始化失败", ex);
                throw new ETException("认证存储初始化失败", "ETOAAuthStorage", ex);
            }
        }

        #endregion 构造函数

        #region 公共方法

        /// <summary>
        /// 保存认证信息
        /// </summary>
        /// <param name="loginInfo">登录信息</param>
        /// <param name="username">用户名（用作存储键）</param>
        public void SaveAuthInfo(ETOALoginInfo loginInfo, string username)
        {
            try
            {
                if (loginInfo == null || string.IsNullOrEmpty(username))
                {
                    throw new ArgumentException("登录信息和用户名不能为空");
                }

                string userSection = $"{AUTH_SECTION}_{username}";

                // 创建INI文件实例
                ETIniFile ini = new ETIniFile(_configPath);

                // 保存基本信息
                ini.WriteString(userSection, "Username", loginInfo.Username ?? "");
                ini.WriteString(userSection, "UserId", loginInfo.UserId ?? "");
                ini.WriteString(userSection, "DisplayName", loginInfo.DisplayName ?? "");
                ini.WriteString(userSection, "Token", loginInfo.Token ?? "");
                ini.WriteString(userSection, "SessionId", loginInfo.SessionId ?? "");
                ini.WriteString(userSection, "BaseUrl", loginInfo.BaseUrl ?? "");
                ini.WriteString(userSection, "LoginUrl", loginInfo.LoginUrl ?? "");
                ini.WriteString(userSection, "RedirectUrl", loginInfo.RedirectUrl ?? "");
                ini.WriteString(userSection, "LoginTime", loginInfo.LoginTime.ToString("yyyy-MM-dd HH:mm:ss"));
                ini.WriteBool(userSection, "IsSuccess", loginInfo.IsSuccess);

                // 保存Cookie信息
                if (loginInfo.Cookies != null && loginInfo.Cookies.Count > 0)
                {
                    string cookieSection = $"{userSection}_Cookies";
                    foreach (var cookie in loginInfo.Cookies)
                    {
                        ini.WriteString(cookieSection, cookie.Key, cookie.Value ?? "");
                    }
                }

                // 保存请求头信息
                if (loginInfo.Headers != null && loginInfo.Headers.Count > 0)
                {
                    string headerSection = $"{userSection}_Headers";
                    foreach (var header in loginInfo.Headers)
                    {
                        ini.WriteString(headerSection, header.Key, header.Value ?? "");
                    }
                }

                ETLogManager.Info($"认证信息保存成功，用户: {username}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"保存认证信息失败，用户: {username}", ex);
                throw new ETException($"保存认证信息失败: {ex.Message}", "SaveAuthInfo", ex);
            }
        }

        /// <summary>
        /// 加载认证信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>登录信息</returns>
        public ETOALoginInfo LoadAuthInfo(string username)
        {
            try
            {
                if (string.IsNullOrEmpty(username))
                {
                    throw new ArgumentException("用户名不能为空");
                }

                string userSection = $"{AUTH_SECTION}_{username}";

                // 创建INI文件实例
                ETIniFile ini = new ETIniFile(_configPath);

                // 检查用户配置是否存在
                string savedUsername = ini.ReadString(userSection, "Username", "");
                if (string.IsNullOrEmpty(savedUsername))
                {
                    ETLogManager.Warn($"未找到用户认证信息: {username}");
                    return null;
                }

                var loginInfo = new ETOALoginInfo
                {
                    Username = ini.ReadString(userSection, "Username", ""),
                    UserId = ini.ReadString(userSection, "UserId", ""),
                    DisplayName = ini.ReadString(userSection, "DisplayName", ""),
                    Token = ini.ReadString(userSection, "Token", ""),
                    SessionId = ini.ReadString(userSection, "SessionId", ""),
                    BaseUrl = ini.ReadString(userSection, "BaseUrl", ""),
                    LoginUrl = ini.ReadString(userSection, "LoginUrl", ""),
                    RedirectUrl = ini.ReadString(userSection, "RedirectUrl", ""),
                    IsSuccess = ini.ReadBool(userSection, "IsSuccess", false)
                };

                // 解析登录时间
                string loginTimeStr = ini.ReadString(userSection, "LoginTime", "");
                if (DateTime.TryParse(loginTimeStr, out DateTime loginTime))
                {
                    loginInfo.LoginTime = loginTime;
                }

                // 加载Cookie信息（简化版本，只加载常见的Cookie）
                string cookieSection = $"{userSection}_Cookies";
                var commonCookieNames = new[] { "JSESSIONID", "sessionid", "session", "token", "auth", "sid", "PHPSESSID", "ASP.NET_SessionId" };
                foreach (string cookieName in commonCookieNames)
                {
                    string value = ini.ReadString(cookieSection, cookieName, "");
                    if (!string.IsNullOrEmpty(value))
                    {
                        loginInfo.SetCookie(cookieName, value);
                    }
                }

                // 加载请求头信息（简化版本，只加载常见的Headers）
                string headerSection = $"{userSection}_Headers";
                var commonHeaderNames = new[] { "Authorization", "X-Auth-Token", "X-CSRF-Token", "User-Agent", "Accept", "Accept-Language" };
                foreach (string headerName in commonHeaderNames)
                {
                    string value = ini.ReadString(headerSection, headerName, "");
                    if (!string.IsNullOrEmpty(value))
                    {
                        loginInfo.SetHeader(headerName, value);
                    }
                }

                ETLogManager.Info($"认证信息加载成功，用户: {username}");
                return loginInfo;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"加载认证信息失败，用户: {username}", ex);
                throw new ETException($"加载认证信息失败: {ex.Message}", "LoadAuthInfo", ex);
            }
        }

        /// <summary>
        /// 删除认证信息
        /// </summary>
        /// <param name="username">用户名</param>
        public void DeleteAuthInfo(string username)
        {
            try
            {
                if (string.IsNullOrEmpty(username))
                {
                    throw new ArgumentException("用户名不能为空");
                }

                string userSection = $"{AUTH_SECTION}_{username}";
                string cookieSection = $"{userSection}_Cookies";
                string headerSection = $"{userSection}_Headers";

                // 创建INI文件实例
                ETIniFile ini = new ETIniFile(_configPath);

                // 删除所有相关配置（通过设置空值）
                ini.WriteString(userSection, "Username", "");
                ini.WriteString(userSection, "UserId", "");
                ini.WriteString(userSection, "DisplayName", "");
                ini.WriteString(userSection, "Token", "");
                ini.WriteString(userSection, "SessionId", "");
                ini.WriteString(userSection, "BaseUrl", "");
                ini.WriteString(userSection, "LoginUrl", "");
                ini.WriteString(userSection, "RedirectUrl", "");
                ini.WriteString(userSection, "LoginTime", "");
                ini.WriteBool(userSection, "IsSuccess", false);

                ETLogManager.Info($"认证信息删除成功，用户: {username}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"删除认证信息失败，用户: {username}", ex);
                throw new ETException($"删除认证信息失败: {ex.Message}", "DeleteAuthInfo", ex);
            }
        }

        /// <summary>
        /// 获取所有已保存的用户名列表
        /// </summary>
        /// <returns>用户名列表</returns>
        public List<string> GetSavedUsernames()
        {
            try
            {
                var usernames = new List<string>();

                // 简化实现：由于ETIniFile可能没有GetSections方法， 这里返回空列表，实际使用时可以通过其他方式维护用户列表
                ETLogManager.Debug("获取已保存用户列表（简化实现）");
                return usernames;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("获取已保存用户列表失败", ex);
                return new List<string>();
            }
        }

        /// <summary>
        /// 检查用户认证信息是否存在
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>是否存在</returns>
        public bool HasAuthInfo(string username)
        {
            try
            {
                if (string.IsNullOrEmpty(username))
                {
                    return false;
                }

                string userSection = $"{AUTH_SECTION}_{username}";
                ETIniFile ini = new ETIniFile(_configPath);
                string savedUsername = ini.ReadString(userSection, "Username", "");
                return !string.IsNullOrEmpty(savedUsername);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"检查认证信息存在性失败，用户: {username}", ex);
                return false;
            }
        }

        /// <summary>
        /// 清理过期的认证信息
        /// </summary>
        /// <param name="expireDays">过期天数</param>
        public void CleanExpiredAuthInfo(int expireDays = 30)
        {
            try
            {
                var usernames = GetSavedUsernames();
                var expireTime = DateTime.Now.AddDays(-expireDays);
                int cleanedCount = 0;
                ETIniFile ini = new ETIniFile(_configPath);

                foreach (string username in usernames)
                {
                    string userSection = $"{AUTH_SECTION}_{username}";
                    string loginTimeStr = ini.ReadString(userSection, "LoginTime", "");

                    if (DateTime.TryParse(loginTimeStr, out DateTime loginTime))
                    {
                        if (loginTime < expireTime)
                        {
                            DeleteAuthInfo(username);
                            cleanedCount++;
                        }
                    }
                }

                ETLogManager.Info($"清理过期认证信息完成，清理了 {cleanedCount} 个过期用户");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("清理过期认证信息失败", ex);
            }
        }

        #endregion 公共方法
    }
}