using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using ET;


namespace ET.ETOAAutomation.Helpers
{
    /// <summary>
    /// 浏览器会话管理器，用于管理多个浏览器实例和会话状态
    /// </summary>
    public static class ETOABrowserSessionManager
    {
        #region 私有字段
        private static readonly Dictionary<string, BrowserSession> _sessions = new Dictionary<string, BrowserSession>();
        private static readonly object _lockObject = new object();
        #endregion

        #region 会话管理方法
        /// <summary>
        /// 创建新的浏览器会话
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <param name="url">初始URL</param>
        /// <returns>浏览器会话</returns>
        public static BrowserSession CreateSession(string sessionId = null, string url = "about:blank")
        {
            try
            {
                if (string.IsNullOrEmpty(sessionId))
                {
                    sessionId = Guid.NewGuid().ToString();
                }

                lock (_lockObject)
                {
                    if (_sessions.ContainsKey(sessionId))
                    {
                        throw new ArgumentException($"会话ID已存在: {sessionId}");
                    }

                    var browser = new ETOASimulationBrowser(url);
                    var session = new BrowserSession
                    {
                        SessionId = sessionId,
                        Browser = browser,
                        CreatedTime = DateTime.Now,
                        LastAccessTime = DateTime.Now,
                        IsActive = true,
                        InitialUrl = url
                    };

                    _sessions[sessionId] = session;
                    
                    // 设置浏览器关闭事件
                    browser.FormClosed += (sender, e) => RemoveSession(sessionId);

                    ETLogManager.Info("ETOABrowserSessionManager", $"创建浏览器会话: {sessionId}");
                    return session;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserSessionManager", $"创建浏览器会话失败: {ex.Message}");
                throw new ETException("创建浏览器会话失败", ex);
            }
        }

        /// <summary>
        /// 获取浏览器会话
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns>浏览器会话</returns>
        public static BrowserSession GetSession(string sessionId)
        {
            try
            {
                lock (_lockObject)
                {
                    if (_sessions.ContainsKey(sessionId))
                    {
                        var session = _sessions[sessionId];
                        session.LastAccessTime = DateTime.Now;
                        return session;
                    }
                    return null;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserSessionManager", $"获取浏览器会话失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 移除浏览器会话
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns>移除是否成功</returns>
        public static bool RemoveSession(string sessionId)
        {
            try
            {
                lock (_lockObject)
                {
                    if (_sessions.ContainsKey(sessionId))
                    {
                        var session = _sessions[sessionId];
                        session.IsActive = false;
                        
                        if (session.Browser != null && !session.Browser.IsDisposed)
                        {
                            session.Browser.Close();
                        }
                        
                        _sessions.Remove(sessionId);
                        ETLogManager.Info("ETOABrowserSessionManager", $"移除浏览器会话: {sessionId}");
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserSessionManager", $"移除浏览器会话失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有活跃会话
        /// </summary>
        /// <returns>活跃会话列表</returns>
        public static List<BrowserSession> GetActiveSessions()
        {
            try
            {
                lock (_lockObject)
                {
                    return _sessions.Values.Where(s => s.IsActive).ToList();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserSessionManager", $"获取活跃会话失败: {ex.Message}");
                return new List<BrowserSession>();
            }
        }

        /// <summary>
        /// 获取会话统计信息
        /// </summary>
        /// <returns>会话统计信息</returns>
        public static SessionStatistics GetSessionStatistics()
        {
            try
            {
                lock (_lockObject)
                {
                    var activeSessions = _sessions.Values.Where(s => s.IsActive).ToList();
                    
                    return new SessionStatistics
                    {
                        TotalSessions = _sessions.Count,
                        ActiveSessions = activeSessions.Count,
                        InactiveSessions = _sessions.Count - activeSessions.Count,
                        OldestSessionTime = activeSessions.Any() ? activeSessions.Min(s => s.CreatedTime) : DateTime.MinValue,
                        NewestSessionTime = activeSessions.Any() ? activeSessions.Max(s => s.CreatedTime) : DateTime.MinValue,
                        AverageSessionAge = activeSessions.Any() ? 
                            TimeSpan.FromTicks((long)activeSessions.Average(s => (DateTime.Now - s.CreatedTime).Ticks)) : 
                            TimeSpan.Zero
                    };
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserSessionManager", $"获取会话统计信息失败: {ex.Message}");
                return new SessionStatistics();
            }
        }

        /// <summary>
        /// 清理过期会话
        /// </summary>
        /// <param name="maxAge">最大会话年龄</param>
        /// <returns>清理的会话数量</returns>
        public static int CleanupExpiredSessions(TimeSpan maxAge)
        {
            try
            {
                var expiredSessions = new List<string>();
                var cutoffTime = DateTime.Now - maxAge;

                lock (_lockObject)
                {
                    foreach (var session in _sessions.Values)
                    {
                        if (session.LastAccessTime < cutoffTime)
                        {
                            expiredSessions.Add(session.SessionId);
                        }
                    }
                }

                foreach (var sessionId in expiredSessions)
                {
                    RemoveSession(sessionId);
                }

                if (expiredSessions.Count > 0)
                {
                    ETLogManager.Info("ETOABrowserSessionManager", 
                        $"清理过期会话: {expiredSessions.Count}个");
                }

                return expiredSessions.Count;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserSessionManager", $"清理过期会话失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 关闭所有会话
        /// </summary>
        /// <returns>关闭的会话数量</returns>
        public static int CloseAllSessions()
        {
            try
            {
                var sessionIds = new List<string>();
                
                lock (_lockObject)
                {
                    sessionIds.AddRange(_sessions.Keys);
                }

                foreach (var sessionId in sessionIds)
                {
                    RemoveSession(sessionId);
                }

                ETLogManager.Info("ETOABrowserSessionManager", $"关闭所有会话: {sessionIds.Count}个");
                return sessionIds.Count;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserSessionManager", $"关闭所有会话失败: {ex.Message}");
                return 0;
            }
        }
        #endregion

        #region 批量操作方法
        /// <summary>
        /// 批量导航到URL
        /// </summary>
        /// <param name="url">目标URL</param>
        /// <param name="sessionIds">会话ID列表（为空则应用到所有活跃会话）</param>
        /// <returns>成功导航的会话数量</returns>
        public static async Task<int> BatchNavigateAsync(string url, List<string> sessionIds = null)
        {
            try
            {
                var targetSessions = sessionIds?.Select(GetSession).Where(s => s != null).ToList() 
                                   ?? GetActiveSessions();

                var successCount = 0;
                var tasks = new List<Task>();

                foreach (var session in targetSessions)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            await session.Browser.NavigateAsync(url);
                            System.Threading.Interlocked.Increment(ref successCount);
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error("ETOABrowserSessionManager", 
                                $"批量导航失败 (会话: {session.SessionId}): {ex.Message}");
                        }
                    }));
                }

                await Task.WhenAll(tasks);
                ETLogManager.Info("ETOABrowserSessionManager", 
                    $"批量导航完成: {successCount}/{targetSessions.Count}");

                return successCount;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserSessionManager", $"批量导航失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 批量执行JavaScript
        /// </summary>
        /// <param name="script">JavaScript代码</param>
        /// <param name="sessionIds">会话ID列表（为空则应用到所有活跃会话）</param>
        /// <returns>执行结果字典</returns>
        public static async Task<Dictionary<string, object>> BatchExecuteScriptAsync(string script, List<string> sessionIds = null)
        {
            try
            {
                var targetSessions = sessionIds?.Select(GetSession).Where(s => s != null).ToList() 
                                   ?? GetActiveSessions();

                var results = new Dictionary<string, object>();
                var tasks = new List<Task>();

                foreach (var session in targetSessions)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            var result = await session.Browser.ExecuteScriptAsync(script);
                            lock (results)
                            {
                                results[session.SessionId] = result;
                            }
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error("ETOABrowserSessionManager", 
                                $"批量执行脚本失败 (会话: {session.SessionId}): {ex.Message}");
                            lock (results)
                            {
                                results[session.SessionId] = null;
                            }
                        }
                    }));
                }

                await Task.WhenAll(tasks);
                ETLogManager.Info("ETOABrowserSessionManager", 
                    $"批量执行脚本完成: {results.Count}/{targetSessions.Count}");

                return results;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOABrowserSessionManager", $"批量执行脚本失败: {ex.Message}");
                return new Dictionary<string, object>();
            }
        }
        #endregion

        #region 内部类
        /// <summary>
        /// 浏览器会话信息
        /// </summary>
        public class BrowserSession
        {
            /// <summary>
            /// 会话ID
            /// </summary>
            public string SessionId { get; set; }

            /// <summary>
            /// 浏览器实例
            /// </summary>
            public ETOASimulationBrowser Browser { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary>
            public DateTime CreatedTime { get; set; }

            /// <summary>
            /// 最后访问时间
            /// </summary>
            public DateTime LastAccessTime { get; set; }

            /// <summary>
            /// 是否活跃
            /// </summary>
            public bool IsActive { get; set; }

            /// <summary>
            /// 初始URL
            /// </summary>
            public string InitialUrl { get; set; }

            /// <summary>
            /// 当前URL
            /// </summary>
            public string CurrentUrl => Browser?.CurrentUrl ?? "";

            /// <summary>
            /// 会话年龄
            /// </summary>
            public TimeSpan Age => DateTime.Now - CreatedTime;

            /// <summary>
            /// 空闲时间
            /// </summary>
            public TimeSpan IdleTime => DateTime.Now - LastAccessTime;
        }

        /// <summary>
        /// 会话统计信息
        /// </summary>
        public class SessionStatistics
        {
            /// <summary>
            /// 总会话数
            /// </summary>
            public int TotalSessions { get; set; }

            /// <summary>
            /// 活跃会话数
            /// </summary>
            public int ActiveSessions { get; set; }

            /// <summary>
            /// 非活跃会话数
            /// </summary>
            public int InactiveSessions { get; set; }

            /// <summary>
            /// 最老会话时间
            /// </summary>
            public DateTime OldestSessionTime { get; set; }

            /// <summary>
            /// 最新会话时间
            /// </summary>
            public DateTime NewestSessionTime { get; set; }

            /// <summary>
            /// 平均会话年龄
            /// </summary>
            public TimeSpan AverageSessionAge { get; set; }
        }
        #endregion
    }
}
