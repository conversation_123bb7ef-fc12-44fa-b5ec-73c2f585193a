# 📊 ETOAModels 数据模型流程详解

## 📋 概述

ETOAModels数据模型集合定义了ETOAAutomation模块中使用的所有数据结构，包括登录信息、API请求响应、会话数据、上传结果、配置信息等核心数据模型。

## 🏗️ 数据模型架构图

```mermaid
classDiagram
    class ETOALoginInfo {
        +string Username
        +string Password
        +string LoginUrl
        +string CaptchaCode
        +bool RememberMe
        +Dictionary~string,string~ AdditionalFields
        +DateTime CreatedTime
        +DateTime LastUsedTime
        +bool IsValid
        
        +Validate() bool
        +Clone() ETOALoginInfo
        +ToSecureString() string
        +FromSecureString(data) ETOALoginInfo
        +ClearSensitiveData() void
    }
    
    class ETOAApiRequest {
        +string Endpoint
        +HttpMethod Method
        +Dictionary~string,object~ Headers
        +object Data
        +int TimeoutSeconds
        +int RetryAttempts
        +bool RequireAuth
        +string RequestId
        +DateTime CreatedTime
        
        +AddHeader(key, value) void
        +SetData(data) void
        +SetTimeout(seconds) void
        +Validate() bool
        +Clone() ETOAApiRequest
    }
    
    class ETOAApiResponse {
        +bool IsSuccess
        +int StatusCode
        +string StatusMessage
        +object Data
        +string RawContent
        +Dictionary~string,string~ Headers
        +TimeSpan Duration
        +string ErrorMessage
        +Exception Exception
        +DateTime ResponseTime
        
        +GetData~T~() T
        +HasError() bool
        +GetErrorDetails() string
        +Clone() ETOAApiResponse
    }
    
    class ETOASessionData {
        +string SessionId
        +string UserId
        +string Username
        +string AuthToken
        +List~Cookie~ Cookies
        +DateTime CreatedTime
        +DateTime LastActivityTime
        +DateTime LastHeartbeatTime
        +DateTime ExpiryTime
        +SessionStatus Status
        +Dictionary~string,object~ Properties
        
        +bool IsValid
        +bool IsExpired
        +bool IsActive
        +TimeSpan RemainingTime
        
        +UpdateActivity() void
        +ExtendExpiry(hours) void
        +AddProperty(key, value) void
        +GetProperty~T~(key) T
        +Clone() ETOASessionData
    }
    
    class ETOAUploadResult {
        +bool IsSuccess
        +string FileName
        +string FilePath
        +long FileSize
        +long UploadedSize
        +double Progress
        +string FileId
        +string FileUrl
        +string ErrorMessage
        +Exception Exception
        +DateTime StartTime
        +DateTime EndTime
        +TimeSpan Duration
        +string ServerResponse
        +int ChunkCount
        +int CompletedChunks
        
        +bool IsCompleted
        +double UploadSpeed
        +TimeSpan EstimatedTimeRemaining
        
        +CalculateSpeed() double
        +GetProgressPercentage() double
        +Clone() ETOAUploadResult
    }
    
    class ETOAConfigInfo {
        +string ServerUrl
        +int LoginTimeout
        +int HeartbeatInterval
        +int SessionExpiryHours
        +int MaxRetryAttempts
        +int MaxFileSize
        +List~string~ AllowedExtensions
        +int ChunkSize
        +int MaxConcurrentUploads
        +bool EnableLogging
        +string LogLevel
        +bool EncryptStorage
        +Dictionary~string,object~ CustomSettings
        
        +Validate() bool
        +LoadFromFile(path) void
        +SaveToFile(path) void
        +Merge(other) void
        +Clone() ETOAConfigInfo
    }
    
    ETOALoginInfo --> ETOASessionData
    ETOAApiRequest --> ETOAApiResponse
    ETOASessionData --> SessionStatus
    ETOAUploadResult --> UploadStatus
    ETOAConfigInfo --> ETOAValidationResult
```

## 🔐 登录信息模型流程

```mermaid
sequenceDiagram
    participant Client as ETOAClient
    participant LoginInfo as ETOALoginInfo
    participant Validator as DataValidator
    participant Crypto as CryptoHelper
    participant Storage as SecureStorage
    
    Client->>LoginInfo: 创建登录信息
    LoginInfo->>LoginInfo: 设置用户名和密码
    LoginInfo->>LoginInfo: 设置登录URL
    
    Client->>LoginInfo: Validate()
    LoginInfo->>Validator: 验证用户名格式
    Validator-->>LoginInfo: 验证结果
    LoginInfo->>Validator: 验证密码强度
    Validator-->>LoginInfo: 验证结果
    LoginInfo->>Validator: 验证URL格式
    Validator-->>LoginInfo: 验证结果
    LoginInfo-->>Client: 返回验证结果
    
    alt 需要保存登录信息
        Client->>LoginInfo: ToSecureString()
        LoginInfo->>Crypto: 加密敏感数据
        Crypto-->>LoginInfo: 返回加密结果
        LoginInfo-->>Client: 返回安全字符串
        
        Client->>Storage: 保存加密数据
        Storage-->>Client: 保存完成
    end
    
    alt 需要加载登录信息
        Client->>Storage: 读取加密数据
        Storage-->>Client: 返回加密字符串
        
        Client->>LoginInfo: FromSecureString(data)
        LoginInfo->>Crypto: 解密数据
        Crypto-->>LoginInfo: 返回解密结果
        LoginInfo->>LoginInfo: 重建对象
        LoginInfo-->>Client: 返回登录信息对象
    end
```

### 登录信息模型实现

```csharp
public class ETOALoginInfo
{
    public string Username { get; set; }
    public string Password { get; set; }
    public string LoginUrl { get; set; }
    public string CaptchaCode { get; set; }
    public bool RememberMe { get; set; }
    public Dictionary<string, string> AdditionalFields { get; set; }
    public DateTime CreatedTime { get; set; }
    public DateTime LastUsedTime { get; set; }
    
    public bool IsValid => Validate();
    
    public ETOALoginInfo()
    {
        AdditionalFields = new Dictionary<string, string>();
        CreatedTime = DateTime.Now;
        LastUsedTime = DateTime.Now;
    }
    
    public bool Validate()
    {
        try
        {
            // 1. 验证用户名
            if (string.IsNullOrWhiteSpace(Username))
            {
                ETLogManager.Warning("用户名不能为空");
                return false;
            }
            
            if (Username.Length < 3 || Username.Length > 50)
            {
                ETLogManager.Warning("用户名长度必须在3-50个字符之间");
                return false;
            }
            
            // 2. 验证密码
            if (string.IsNullOrWhiteSpace(Password))
            {
                ETLogManager.Warning("密码不能为空");
                return false;
            }
            
            if (Password.Length < 6)
            {
                ETLogManager.Warning("密码长度不能少于6个字符");
                return false;
            }
            
            // 3. 验证登录URL
            if (string.IsNullOrWhiteSpace(LoginUrl))
            {
                ETLogManager.Warning("登录URL不能为空");
                return false;
            }
            
            if (!Uri.TryCreate(LoginUrl, UriKind.Absolute, out var uri) || 
                (uri.Scheme != "http" && uri.Scheme != "https"))
            {
                ETLogManager.Warning($"无效的登录URL格式: {LoginUrl}");
                return false;
            }
            
            return true;
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"登录信息验证异常: {ex.Message}", ex);
            return false;
        }
    }
    
    public ETOALoginInfo Clone()
    {
        return new ETOALoginInfo
        {
            Username = this.Username,
            Password = this.Password,
            LoginUrl = this.LoginUrl,
            CaptchaCode = this.CaptchaCode,
            RememberMe = this.RememberMe,
            AdditionalFields = new Dictionary<string, string>(this.AdditionalFields),
            CreatedTime = this.CreatedTime,
            LastUsedTime = this.LastUsedTime
        };
    }
    
    public string ToSecureString()
    {
        try
        {
            var data = new
            {
                Username = this.Username,
                Password = ETOAStorageHelper.EncryptData(this.Password, "login_password"),
                LoginUrl = this.LoginUrl,
                CaptchaCode = this.CaptchaCode,
                RememberMe = this.RememberMe,
                AdditionalFields = this.AdditionalFields,
                CreatedTime = this.CreatedTime,
                LastUsedTime = this.LastUsedTime
            };
            
            var json = ETOAJsonHelper.ToJson(data);
            return ETOAStorageHelper.EncryptData(json, "login_info");
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"登录信息加密异常: {ex.Message}", ex);
            throw;
        }
    }
    
    public static ETOALoginInfo FromSecureString(string secureData)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(secureData))
                return null;
            
            var json = ETOAStorageHelper.DecryptData(secureData, "login_info");
            var data = ETOAJsonHelper.FromJson<dynamic>(json);
            
            var loginInfo = new ETOALoginInfo
            {
                Username = data.Username,
                Password = ETOAStorageHelper.DecryptData(data.Password, "login_password"),
                LoginUrl = data.LoginUrl,
                CaptchaCode = data.CaptchaCode,
                RememberMe = data.RememberMe,
                AdditionalFields = data.AdditionalFields?.ToObject<Dictionary<string, string>>() ?? new Dictionary<string, string>(),
                CreatedTime = data.CreatedTime,
                LastUsedTime = data.LastUsedTime
            };
            
            return loginInfo;
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"登录信息解密异常: {ex.Message}", ex);
            return null;
        }
    }
    
    public void ClearSensitiveData()
    {
        Password = null;
        CaptchaCode = null;
        AdditionalFields?.Clear();
    }
}
```

## 🌐 API请求响应模型流程

```mermaid
flowchart TD
    A[创建API请求] --> B[设置请求参数]
    B --> C[添加请求头]
    C --> D[设置请求数据]
    D --> E[验证请求有效性]
    E --> F{验证是否通过}
    F -->|否| G[返回验证错误]
    F -->|是| H[发送HTTP请求]
    H --> I[等待服务器响应]
    I --> J[创建响应对象]
    J --> K[解析响应状态]
    K --> L[解析响应头]
    L --> M[解析响应数据]
    M --> N[计算响应时间]
    N --> O{响应是否成功}
    O -->|是| P[设置成功状态]
    O -->|否| Q[设置错误信息]
    P --> R[返回响应对象]
    Q --> R
    
    style A fill:#e1f5fe
    style R fill:#c8e6c9
    style G fill:#ffcdd2
```

### API请求响应模型实现

```csharp
public class ETOAApiRequest
{
    public string Endpoint { get; set; }
    public HttpMethod Method { get; set; }
    public Dictionary<string, object> Headers { get; set; }
    public object Data { get; set; }
    public int TimeoutSeconds { get; set; }
    public int RetryAttempts { get; set; }
    public bool RequireAuth { get; set; }
    public string RequestId { get; set; }
    public DateTime CreatedTime { get; set; }
    
    public ETOAApiRequest()
    {
        Headers = new Dictionary<string, object>();
        Method = HttpMethod.Get;
        TimeoutSeconds = 30;
        RetryAttempts = 3;
        RequireAuth = true;
        RequestId = Guid.NewGuid().ToString();
        CreatedTime = DateTime.Now;
    }
    
    public void AddHeader(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key) || value == null)
            return;
        
        Headers[key] = value;
    }
    
    public void SetData(object data)
    {
        Data = data;
    }
    
    public void SetTimeout(int seconds)
    {
        if (seconds > 0)
            TimeoutSeconds = seconds;
    }
    
    public bool Validate()
    {
        try
        {
            // 验证端点URL
            if (string.IsNullOrWhiteSpace(Endpoint))
                return false;
            
            // 验证HTTP方法
            if (Method == null)
                return false;
            
            // 验证超时时间
            if (TimeoutSeconds <= 0 || TimeoutSeconds > 300)
                return false;
            
            // 验证重试次数
            if (RetryAttempts < 0 || RetryAttempts > 10)
                return false;
            
            return true;
        }
        catch
        {
            return false;
        }
    }
    
    public ETOAApiRequest Clone()
    {
        return new ETOAApiRequest
        {
            Endpoint = this.Endpoint,
            Method = this.Method,
            Headers = new Dictionary<string, object>(this.Headers),
            Data = this.Data,
            TimeoutSeconds = this.TimeoutSeconds,
            RetryAttempts = this.RetryAttempts,
            RequireAuth = this.RequireAuth,
            RequestId = Guid.NewGuid().ToString(),
            CreatedTime = DateTime.Now
        };
    }
}

public class ETOAApiResponse
{
    public bool IsSuccess { get; set; }
    public int StatusCode { get; set; }
    public string StatusMessage { get; set; }
    public object Data { get; set; }
    public string RawContent { get; set; }
    public Dictionary<string, string> Headers { get; set; }
    public TimeSpan Duration { get; set; }
    public string ErrorMessage { get; set; }
    public Exception Exception { get; set; }
    public DateTime ResponseTime { get; set; }
    
    public ETOAApiResponse()
    {
        Headers = new Dictionary<string, string>();
        ResponseTime = DateTime.Now;
    }
    
    public T GetData<T>()
    {
        try
        {
            if (Data == null)
                return default(T);
            
            if (Data is T directCast)
                return directCast;
            
            if (Data is string jsonString)
                return ETOAJsonHelper.FromJson<T>(jsonString);
            
            var json = ETOAJsonHelper.ToJson(Data);
            return ETOAJsonHelper.FromJson<T>(json);
        }
        catch (Exception ex)
        {
            ETLogManager.Warning($"响应数据转换失败: {typeof(T).Name}, 错误: {ex.Message}");
            return default(T);
        }
    }
    
    public bool HasError()
    {
        return !IsSuccess || !string.IsNullOrEmpty(ErrorMessage) || Exception != null;
    }
    
    public string GetErrorDetails()
    {
        var details = new List<string>();
        
        if (!IsSuccess)
            details.Add($"请求失败: {StatusCode} {StatusMessage}");
        
        if (!string.IsNullOrEmpty(ErrorMessage))
            details.Add($"错误信息: {ErrorMessage}");
        
        if (Exception != null)
            details.Add($"异常: {Exception.Message}");
        
        return string.Join("; ", details);
    }
    
    public ETOAApiResponse Clone()
    {
        return new ETOAApiResponse
        {
            IsSuccess = this.IsSuccess,
            StatusCode = this.StatusCode,
            StatusMessage = this.StatusMessage,
            Data = this.Data,
            RawContent = this.RawContent,
            Headers = new Dictionary<string, string>(this.Headers),
            Duration = this.Duration,
            ErrorMessage = this.ErrorMessage,
            Exception = this.Exception,
            ResponseTime = this.ResponseTime
        };
    }
}
```

---

**📅 文档版本**: v1.0  
**🔄 最后更新**: 2024年12月  
**👨‍💻 维护团队**: ETOAAutomation开发组
