using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using Flurl.Http;
using ET;

namespace ET.ETOAAutomation.Helpers
{
    /// <summary>
    /// Cookie处理辅助类，提供Cookie的解析、构建和管理功能
    /// </summary>
    public static class ETOACookieHelper
    {
        #region Cookie解析方法
        /// <summary>
        /// 解析Cookie字符串为字典
        /// </summary>
        /// <param name="cookieString">Cookie字符串</param>
        /// <returns>Cookie字典</returns>
        public static Dictionary<string, string> ParseCookieString(string cookieString)
        {
            var cookies = new Dictionary<string, string>();
            
            try
            {
                if (string.IsNullOrEmpty(cookieString)) return cookies;
                
                var cookiePairs = cookieString.Split(';');
                foreach (var pair in cookiePairs)
                {
                    var trimmedPair = pair.Trim();
                    if (string.IsNullOrEmpty(trimmedPair)) continue;
                    
                    var equalIndex = trimmedPair.IndexOf('=');
                    if (equalIndex > 0)
                    {
                        var name = trimmedPair.Substring(0, equalIndex).Trim();
                        var value = trimmedPair.Substring(equalIndex + 1).Trim();
                        
                        if (!string.IsNullOrEmpty(name))
                        {
                            cookies[name] = value;
                        }
                    }
                }
                
                ETLogManager.Info($"解析Cookie成功，共{cookies.Count}个Cookie项");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"解析Cookie字符串失败: {ex.Message}", ex);
                throw new ETException($"解析Cookie字符串失败: {ex.Message}", "ParseCookieString", ex);
            }
            
            return cookies;
        }

        /// <summary>
        /// 解析Set-Cookie响应头
        /// </summary>
        /// <param name="setCookieHeaders">Set-Cookie响应头列表</param>
        /// <returns>Cookie字典</returns>
        public static Dictionary<string, string> ParseSetCookieHeaders(IEnumerable<string> setCookieHeaders)
        {
            var cookies = new Dictionary<string, string>();
            
            try
            {
                if (setCookieHeaders == null) return cookies;
                
                foreach (var header in setCookieHeaders)
                {
                    if (string.IsNullOrEmpty(header)) continue;
                    
                    // Set-Cookie格式: name=value; Path=/; Domain=.example.com; HttpOnly; Secure
                    var parts = header.Split(';');
                    if (parts.Length > 0)
                    {
                        var mainPart = parts[0].Trim();
                        var equalIndex = mainPart.IndexOf('=');
                        
                        if (equalIndex > 0)
                        {
                            var name = mainPart.Substring(0, equalIndex).Trim();
                            var value = mainPart.Substring(equalIndex + 1).Trim();
                            
                            if (!string.IsNullOrEmpty(name))
                            {
                                cookies[name] = value;
                            }
                        }
                    }
                }
                
                ETLogManager.Info($"解析Set-Cookie头成功，共{cookies.Count}个Cookie项");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"解析Set-Cookie头失败: {ex.Message}", ex);
                throw new ETException($"解析Set-Cookie头失败: {ex.Message}", "ParseSetCookieHeaders", ex);
            }
            
            return cookies;
        }
        #endregion

        #region Cookie构建方法
        /// <summary>
        /// 将Cookie字典构建为Cookie字符串
        /// </summary>
        /// <param name="cookies">Cookie字典</param>
        /// <returns>Cookie字符串</returns>
        public static string BuildCookieString(Dictionary<string, string> cookies)
        {
            try
            {
                if (cookies == null || cookies.Count == 0) return string.Empty;
                
                var cookiePairs = cookies
                    .Where(kv => !string.IsNullOrEmpty(kv.Key))
                    .Select(kv => $"{kv.Key}={kv.Value ?? string.Empty}");
                
                var result = string.Join("; ", cookiePairs);
                ETLogManager.Info($"构建Cookie字符串成功，长度: {result.Length}");
                
                return result;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"构建Cookie字符串失败: {ex.Message}", ex);
                throw new ETException($"构建Cookie字符串失败: {ex.Message}", "BuildCookieString", ex);
            }
        }

        /// <summary>
        /// 创建CookieJar对象
        /// </summary>
        /// <param name="cookies">Cookie字典</param>
        /// <param name="domain">域名</param>
        /// <param name="path">路径</param>
        /// <returns>CookieJar对象</returns>
        public static CookieJar CreateCookieJar(Dictionary<string, string> cookies, string domain = null, string path = "/")
        {
            try
            {
                var jar = new CookieJar();
                
                if (cookies != null)
                {
                    foreach (var cookie in cookies)
                    {
                        if (!string.IsNullOrEmpty(cookie.Key))
                        {
                            // 构建originUrl，如果domain为空则使用默认值
                            var originUrl = string.IsNullOrEmpty(domain) ? "http://localhost" : $"http://{domain.TrimStart('.')}";
                            jar.AddOrReplace(cookie.Key, cookie.Value, originUrl);
                        }
                    }
                }
                
                ETLogManager.Info($"创建CookieJar成功，包含{cookies?.Count ?? 0}个Cookie");
                return jar;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"创建CookieJar失败: {ex.Message}", ex);
                throw new ETException($"创建CookieJar失败: {ex.Message}", "CreateCookieJar", ex);
            }
        }
        #endregion

        #region Cookie管理方法
        /// <summary>
        /// 合并两个Cookie字典
        /// </summary>
        /// <param name="cookies1">第一个Cookie字典</param>
        /// <param name="cookies2">第二个Cookie字典</param>
        /// <param name="overwrite">是否覆盖重复的Cookie</param>
        /// <returns>合并后的Cookie字典</returns>
        public static Dictionary<string, string> MergeCookies(
            Dictionary<string, string> cookies1, 
            Dictionary<string, string> cookies2, 
            bool overwrite = true)
        {
            try
            {
                var result = new Dictionary<string, string>();
                
                // 添加第一个字典的Cookie
                if (cookies1 != null)
                {
                    foreach (var cookie in cookies1)
                    {
                        result[cookie.Key] = cookie.Value;
                    }
                }
                
                // 添加第二个字典的Cookie
                if (cookies2 != null)
                {
                    foreach (var cookie in cookies2)
                    {
                        if (overwrite || !result.ContainsKey(cookie.Key))
                        {
                            result[cookie.Key] = cookie.Value;
                        }
                    }
                }
                
                ETLogManager.Info($"合并Cookie成功，结果包含{result.Count}个Cookie");
                return result;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"合并Cookie失败: {ex.Message}", ex);
                throw new ETException($"合并Cookie失败: {ex.Message}", "MergeCookies", ex);
            }
        }

        /// <summary>
        /// 过滤Cookie字典
        /// </summary>
        /// <param name="cookies">原始Cookie字典</param>
        /// <param name="includeNames">要包含的Cookie名称列表</param>
        /// <param name="excludeNames">要排除的Cookie名称列表</param>
        /// <returns>过滤后的Cookie字典</returns>
        public static Dictionary<string, string> FilterCookies(
            Dictionary<string, string> cookies,
            IEnumerable<string> includeNames = null,
            IEnumerable<string> excludeNames = null)
        {
            try
            {
                if (cookies == null) return new Dictionary<string, string>();
                
                var result = new Dictionary<string, string>(cookies);
                
                // 如果指定了包含列表，只保留指定的Cookie
                if (includeNames != null)
                {
                    var includeSet = new HashSet<string>(includeNames, StringComparer.OrdinalIgnoreCase);
                    result = result.Where(kv => includeSet.Contains(kv.Key))
                                  .ToDictionary(kv => kv.Key, kv => kv.Value);
                }
                
                // 如果指定了排除列表，移除指定的Cookie
                if (excludeNames != null)
                {
                    var excludeSet = new HashSet<string>(excludeNames, StringComparer.OrdinalIgnoreCase);
                    result = result.Where(kv => !excludeSet.Contains(kv.Key))
                                  .ToDictionary(kv => kv.Key, kv => kv.Value);
                }
                
                ETLogManager.Info($"过滤Cookie成功，从{cookies.Count}个减少到{result.Count}个");
                return result;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"过滤Cookie失败: {ex.Message}", ex);
                throw new ETException($"过滤Cookie失败: {ex.Message}", "FilterCookies", ex);
            }
        }

        /// <summary>
        /// 检查Cookie是否包含指定的名称
        /// </summary>
        /// <param name="cookies">Cookie字典</param>
        /// <param name="cookieName">Cookie名称</param>
        /// <returns>是否包含</returns>
        public static bool ContainsCookie(Dictionary<string, string> cookies, string cookieName)
        {
            if (cookies == null || string.IsNullOrEmpty(cookieName)) return false;
            
            return cookies.ContainsKey(cookieName);
        }

        /// <summary>
        /// 获取Cookie值
        /// </summary>
        /// <param name="cookies">Cookie字典</param>
        /// <param name="cookieName">Cookie名称</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>Cookie值</returns>
        public static string GetCookieValue(Dictionary<string, string> cookies, string cookieName, string defaultValue = null)
        {
            if (cookies == null || string.IsNullOrEmpty(cookieName)) return defaultValue;
            
            return cookies.ContainsKey(cookieName) ? cookies[cookieName] : defaultValue;
        }

        /// <summary>
        /// 设置Cookie值
        /// </summary>
        /// <param name="cookies">Cookie字典</param>
        /// <param name="cookieName">Cookie名称</param>
        /// <param name="cookieValue">Cookie值</param>
        public static void SetCookieValue(Dictionary<string, string> cookies, string cookieName, string cookieValue)
        {
            if (cookies == null || string.IsNullOrEmpty(cookieName)) return;
            
            cookies[cookieName] = cookieValue ?? string.Empty;
        }

        /// <summary>
        /// 移除Cookie
        /// </summary>
        /// <param name="cookies">Cookie字典</param>
        /// <param name="cookieName">Cookie名称</param>
        /// <returns>是否成功移除</returns>
        public static bool RemoveCookie(Dictionary<string, string> cookies, string cookieName)
        {
            if (cookies == null || string.IsNullOrEmpty(cookieName)) return false;
            
            return cookies.Remove(cookieName);
        }
        #endregion

        #region Cookie编码解码方法
        /// <summary>
        /// URL编码Cookie值
        /// </summary>
        /// <param name="value">原始值</param>
        /// <returns>编码后的值</returns>
        public static string EncodeCookieValue(string value)
        {
            if (string.IsNullOrEmpty(value)) return value;
            
            try
            {
                return HttpUtility.UrlEncode(value);
            }
            catch (Exception ex)
            {
                ETLogManager.Warn($"Cookie值编码失败: {ex.Message}");
                return value;
            }
        }

        /// <summary>
        /// URL解码Cookie值
        /// </summary>
        /// <param name="encodedValue">编码后的值</param>
        /// <returns>解码后的值</returns>
        public static string DecodeCookieValue(string encodedValue)
        {
            if (string.IsNullOrEmpty(encodedValue)) return encodedValue;
            
            try
            {
                return HttpUtility.UrlDecode(encodedValue);
            }
            catch (Exception ex)
            {
                ETLogManager.Warn($"Cookie值解码失败: {ex.Message}");
                return encodedValue;
            }
        }
        #endregion

        #region Cookie持久化方法
        /// <summary>
        /// 将Cookie字典序列化为字符串（用于持久化存储）
        /// </summary>
        /// <param name="cookies">Cookie字典</param>
        /// <returns>序列化字符串</returns>
        public static string SerializeCookies(Dictionary<string, string> cookies)
        {
            try
            {
                if (cookies == null || cookies.Count == 0) return string.Empty;
                
                return ETOAJsonHelper.ToJson(cookies);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"Cookie序列化失败: {ex.Message}", ex);
                throw new ETException($"Cookie序列化失败: {ex.Message}", "SerializeCookies", ex);
            }
        }

        /// <summary>
        /// 从序列化字符串反序列化Cookie字典
        /// </summary>
        /// <param name="serializedCookies">序列化字符串</param>
        /// <returns>Cookie字典</returns>
        public static Dictionary<string, string> DeserializeCookies(string serializedCookies)
        {
            try
            {
                if (string.IsNullOrEmpty(serializedCookies)) return new Dictionary<string, string>();
                
                return ETOAJsonHelper.FromJsonSafe<Dictionary<string, string>>(serializedCookies, new Dictionary<string, string>());
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"Cookie反序列化失败: {ex.Message}", ex);
                return new Dictionary<string, string>();
            }
        }
        #endregion
    }
}
